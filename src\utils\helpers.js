// Helper Functions

/**
 * Format date to Arabic locale
 * @param {string} dateString - Date string
 * @returns {string} Formatted date
 */
export const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('ar-EG', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

/**
 * Format time to 12-hour format with Arabic AM/PM
 * @param {string} timeString - Time string (HH:MM)
 * @returns {string} Formatted time
 */
export const formatTime = (timeString) => {
  const [hours, minutes] = timeString.split(':')
  const hour = parseInt(hours)

  if (hour === 0) {
    return `12:${minutes} منتصف الليل`
  } else if (hour === 12) {
    return `12:${minutes} ظهراً`
  } else if (hour < 12) {
    return `${hour}:${minutes} صباحاً`
  } else {
    return `${hour - 12}:${minutes} مساءً`
  }
}

/**
 * Format phone number
 * @param {string} phone - Phone number
 * @returns {string} Formatted phone number
 */
export const formatPhone = (phone) => {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '')
  
  // Format as Egyptian phone number
  if (cleaned.length === 11 && cleaned.startsWith('01')) {
    return `+20 ${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`
  }
  
  return phone
}

/**
 * Validate phone number
 * @param {string} phone - Phone number
 * @returns {boolean} Is valid
 */
export const isValidPhone = (phone) => {
  const cleaned = phone.replace(/\D/g, '')
  return cleaned.length >= 10 && cleaned.length <= 15
}

/**
 * Validate name
 * @param {string} name - Name
 * @returns {boolean} Is valid
 */
export const isValidName = (name) => {
  return name.trim().length >= 2 && name.trim().length <= 50
}

/**
 * Get status color class
 * @param {string} status - Appointment status
 * @returns {string} CSS classes
 */
export const getStatusColor = (status) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/50'
    case 'accepted':
      return 'bg-green-500/20 text-green-400 border-green-500/50'
    case 'rejected':
      return 'bg-red-500/20 text-red-400 border-red-500/50'
    default:
      return 'bg-gray-500/20 text-gray-400 border-gray-500/50'
  }
}

/**
 * Generate time slots for a day
 * @param {string} startTime - Start time (HH:MM)
 * @param {string} endTime - End time (HH:MM)
 * @param {number} intervalMinutes - Interval in minutes
 * @param {string[]} excludeSlots - Time slots to exclude
 * @returns {string[]} Array of time slots
 */
export const generateTimeSlots = (startTime, endTime, intervalMinutes = 60, excludeSlots = []) => {
  const slots = []
  const start = new Date(`2000-01-01T${startTime}:00`)
  const end = new Date(`2000-01-01T${endTime}:00`)
  
  let current = new Date(start)
  
  while (current < end) {
    const timeString = current.toTimeString().slice(0, 5)
    if (!excludeSlots.includes(timeString)) {
      slots.push(timeString)
    }
    current.setMinutes(current.getMinutes() + intervalMinutes)
  }
  
  return slots
}

/**
 * Check if date is in the future
 * @param {string} dateString - Date string
 * @returns {boolean} Is future date
 */
export const isFutureDate = (dateString) => {
  const date = new Date(dateString)
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return date >= today
}

/**
 * Debounce function
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
export const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}
