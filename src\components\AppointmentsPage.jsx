import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { CalendarIcon, ClockIcon, UserIcon, PhoneIcon, CheckCircleIcon } from '@heroicons/react/24/outline'
import { appointmentService } from '../lib/supabase'
import { Link } from 'react-router-dom'
import { formatDate, formatTime, isValidName, isValidPhone } from '../utils/helpers'
import useNotification from '../hooks/useNotification'
import Notification from './Notification'

const AppointmentsPage = () => {
  const [availableSlots, setAvailableSlots] = useState([])
  const [selectedSlot, setSelectedSlot] = useState(null)
  const [formData, setFormData] = useState({
    name: '',
    phone: ''
  })
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [success, setSuccess] = useState(false)
  const [error, setError] = useState('')

  const { notification, showSuccess, showError } = useNotification()

  useEffect(() => {
    loadAvailableSlots()
  }, [])

  const loadAvailableSlots = async () => {
    try {
      const slots = await appointmentService.getAvailableSlots()
      setAvailableSlots(slots)
    } catch (err) {
      setError('حدث خطأ في تحميل المواعيد المتاحة')
      console.error('Error loading slots:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleSlotSelect = (slot) => {
    setSelectedSlot(slot)
    setSuccess(false)
    setError('')
  }

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!selectedSlot) {
      showError('يرجى اختيار موعد أولاً')
      return
    }

    if (!isValidName(formData.name)) {
      showError('يرجى إدخال اسم صحيح (2-50 حرف)')
      return
    }

    if (!isValidPhone(formData.phone)) {
      showError('يرجى إدخال رقم هاتف صحيح')
      return
    }

    setSubmitting(true)
    setError('')

    try {
      await appointmentService.createAppointment({
        name: formData.name.trim(),
        phone: formData.phone.trim(),
        date: selectedSlot.date,
        time: selectedSlot.time
      })

      setSuccess(true)
      setFormData({ name: '', phone: '' })
      setSelectedSlot(null)
      showSuccess('تم إرسال طلب الحجز بنجاح! سيتم التواصل معك قريباً')
    } catch (err) {
      showError('حدث خطأ في إرسال الطلب. يرجى المحاولة مرة أخرى')
      console.error('Error creating appointment:', err)
    } finally {
      setSubmitting(false)
    }
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-EG', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTime = (timeString) => {
    const [hours, minutes] = timeString.split(':')
    const hour = parseInt(hours)
    const ampm = hour >= 12 ? 'م' : 'ص'
    const displayHour = hour > 12 ? hour - 12 : hour === 0 ? 12 : hour
    return `${displayHour}:${minutes} ${ampm}`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-white">جاري تحميل المواعيد المتاحة...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="bg-slate-800/50 backdrop-blur-sm border-b border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <Link to="/" className="text-2xl font-bold text-white hover:text-blue-400 transition-colors">
              حلاقة عبدالله محمود
            </Link>
            <Link to="/admin" className="text-gray-300 hover:text-white transition-colors">
              تسجيل دخول الإدارة
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {success ? (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-green-500/20 border border-green-500/50 rounded-2xl p-8 text-center"
          >
            <CheckCircleIcon className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-white mb-4">تم إرسال طلبك بنجاح!</h2>
            <p className="text-gray-300 mb-6">
              سيتم مراجعة طلبك من قِبل الحلاق وستحصل على تأكيد قريباً
            </p>
            <button
              onClick={() => setSuccess(false)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
            >
              حجز موعد آخر
            </button>
          </motion.div>
        ) : (
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Available Slots */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-3xl font-bold text-white mb-8 flex items-center">
                <CalendarIcon className="h-8 w-8 ml-3 text-blue-500" />
                المواعيد المتاحة
              </h2>

              {availableSlots.length === 0 ? (
                <div className="bg-slate-700/50 rounded-2xl p-8 text-center">
                  <p className="text-gray-300">لا توجد مواعيد متاحة حالياً</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {availableSlots.map((slot) => (
                    <motion.button
                      key={slot.id}
                      onClick={() => handleSlotSelect(slot)}
                      className={`w-full p-4 rounded-xl border-2 transition-all duration-300 text-right ${
                        selectedSlot?.id === slot.id
                          ? 'border-blue-500 bg-blue-500/20'
                          : 'border-slate-600 bg-slate-700/50 hover:border-slate-500'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-white font-semibold">{formatDate(slot.date)}</p>
                          <p className="text-gray-300 flex items-center">
                            <ClockIcon className="h-4 w-4 ml-1" />
                            {formatTime(slot.time)}
                          </p>
                        </div>
                        {selectedSlot?.id === slot.id && (
                          <CheckCircleIcon className="h-6 w-6 text-blue-500" />
                        )}
                      </div>
                    </motion.button>
                  ))}
                </div>
              )}
            </motion.div>

            {/* Booking Form */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-3xl font-bold text-white mb-8">معلومات الحجز</h2>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-white font-semibold mb-2">
                    <UserIcon className="h-5 w-5 inline ml-2" />
                    الاسم الكامل
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors"
                    placeholder="أدخل اسمك الكامل"
                    required
                  />
                </div>

                <div>
                  <label className="block text-white font-semibold mb-2">
                    <PhoneIcon className="h-5 w-5 inline ml-2" />
                    رقم الهاتف
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors"
                    placeholder="أدخل رقم هاتفك"
                    required
                  />
                </div>

                {selectedSlot && (
                  <div className="bg-blue-500/20 border border-blue-500/50 rounded-lg p-4">
                    <h3 className="text-white font-semibold mb-2">الموعد المختار:</h3>
                    <p className="text-gray-300">{formatDate(selectedSlot.date)}</p>
                    <p className="text-gray-300">{formatTime(selectedSlot.time)}</p>
                  </div>
                )}

                {error && (
                  <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-4">
                    <p className="text-red-400">{error}</p>
                  </div>
                )}

                <button
                  type="submit"
                  disabled={submitting || !selectedSlot}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-600 text-white font-semibold py-4 rounded-lg transition-all duration-300 disabled:cursor-not-allowed"
                >
                  {submitting ? 'جاري الإرسال...' : 'تأكيد الحجز'}
                </button>
              </form>
            </motion.div>
          </div>
        )}
      </div>
    </div>
  )
}

export default AppointmentsPage
