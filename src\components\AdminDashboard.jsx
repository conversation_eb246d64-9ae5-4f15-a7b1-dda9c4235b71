import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  ClockIcon, 
  UserIcon, 
  PhoneIcon,
  CalendarIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline'
import { appointmentService, notificationService } from '../lib/supabase'

const AdminDashboard = ({ user, onLogout }) => {
  const [appointments, setAppointments] = useState([])
  const [loading, setLoading] = useState(true)
  const [processingId, setProcessingId] = useState(null)
  const [filter, setFilter] = useState('all') // all, pending, accepted, rejected

  useEffect(() => {
    loadAppointments()
  }, [])

  const loadAppointments = async () => {
    try {
      const data = await appointmentService.getAllAppointments()
      setAppointments(data)
    } catch (error) {
      console.error('Error loading appointments:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleStatusUpdate = async (appointmentId, newStatus, date, time, customerId, customerName) => {
    setProcessingId(appointmentId)

    try {
      await appointmentService.updateAppointmentStatus(appointmentId, newStatus)

      // Update slot availability based on status
      if (newStatus === 'accepted') {
        await appointmentService.markSlotAsBooked(date, time)
        // Send notification to customer
        await notificationService.createNotification(
          customerId,
          'تم قبول حجزك',
          `تم قبول حجزك ليوم ${date} في تمام الساعة ${time}. نتطلع لرؤيتك!`,
          'success'
        )
      } else if (newStatus === 'rejected') {
        await appointmentService.markSlotAsAvailable(date, time)
        // Send notification to customer
        await notificationService.createNotification(
          customerId,
          'تم رفض حجزك',
          `نعتذر، تم رفض حجزك ليوم ${date} في تمام الساعة ${time}. يرجى اختيار موعد آخر.`,
          'error'
        )
      }

      // Reload appointments
      await loadAppointments()
    } catch (error) {
      console.error('Error updating appointment:', error)
    } finally {
      setProcessingId(null)
    }
  }

  const handleLogout = () => {
    onLogout()
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-EG', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTime = (timeString) => {
    const [hours, minutes] = timeString.split(':')
    const hour = parseInt(hours)
    const ampm = hour >= 12 ? 'م' : 'ص'
    const displayHour = hour > 12 ? hour - 12 : hour === 0 ? 12 : hour
    return `${displayHour}:${minutes} ${ampm}`
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/50'
      case 'accepted':
        return 'bg-green-500/20 text-green-400 border-green-500/50'
      case 'rejected':
        return 'bg-red-500/20 text-red-400 border-red-500/50'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/50'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'pending':
        return 'في الانتظار'
      case 'accepted':
        return 'مقبول'
      case 'rejected':
        return 'مرفوض'
      default:
        return status
    }
  }

  const filteredAppointments = appointments.filter(appointment => {
    if (filter === 'all') return true
    return appointment.status === filter
  })

  const stats = {
    total: appointments.length,
    pending: appointments.filter(a => a.status === 'pending').length,
    accepted: appointments.filter(a => a.status === 'accepted').length,
    rejected: appointments.filter(a => a.status === 'rejected').length
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-white">جاري تحميل الحجوزات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
      {/* Professional Header */}
      <header className="bg-black/50 backdrop-blur-md border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="bg-gradient-to-r from-blue-600 to-cyan-600 p-3 rounded-lg ml-4">
                <UserIcon className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white">لوحة تحكم الإدارة</h1>
                <p className="text-gray-400">إدارة الحجوزات والمواعيد</p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              className="flex items-center px-4 py-2 bg-red-600/20 hover:bg-red-600/30 text-white rounded-lg transition-colors border border-red-600/30"
            >
              <ArrowRightOnRectangleIcon className="h-5 w-5 ml-2" />
              تسجيل الخروج
            </button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Professional Stats Dashboard */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-r from-blue-600/20 to-cyan-600/20 border border-blue-600/30 rounded-xl p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-300 text-sm font-medium">إجمالي الحجوزات</p>
                <p className="text-3xl font-bold text-white">{stats.total}</p>
              </div>
              <CalendarIcon className="h-12 w-12 text-blue-400" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-gradient-to-r from-yellow-600/20 to-orange-600/20 border border-yellow-600/30 rounded-xl p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-yellow-300 text-sm font-medium">في الانتظار</p>
                <p className="text-3xl font-bold text-white">{stats.pending}</p>
              </div>
              <ClockIcon className="h-12 w-12 text-yellow-400" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-gradient-to-r from-green-600/20 to-emerald-600/20 border border-green-600/30 rounded-xl p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-300 text-sm font-medium">مقبولة</p>
                <p className="text-3xl font-bold text-white">{stats.accepted}</p>
              </div>
              <CheckCircleIcon className="h-12 w-12 text-green-400" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="bg-gradient-to-r from-red-600/20 to-pink-600/20 border border-red-600/30 rounded-xl p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-red-300 text-sm font-medium">مرفوضة</p>
                <p className="text-3xl font-bold text-white">{stats.rejected}</p>
              </div>
              <XCircleIcon className="h-12 w-12 text-red-400" />
            </div>
          </motion.div>
        </div>

        {/* Professional Filter Buttons */}
        <div className="flex flex-wrap gap-3 mb-8 bg-black/30 backdrop-blur-md rounded-xl p-4">
          {[
            { key: 'all', label: 'جميع الحجوزات', color: 'blue' },
            { key: 'pending', label: 'في الانتظار', color: 'yellow' },
            { key: 'accepted', label: 'مقبولة', color: 'green' },
            { key: 'rejected', label: 'مرفوضة', color: 'red' }
          ].map((filterOption) => (
            <button
              key={filterOption.key}
              onClick={() => setFilter(filterOption.key)}
              className={`px-6 py-3 rounded-lg font-medium transition-all ${
                filter === filterOption.key
                  ? `bg-${filterOption.color}-600 text-white shadow-lg scale-105`
                  : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50 hover:text-white'
              }`}
            >
              {filterOption.label}
            </button>
          ))}
        </div>

        {/* Professional Appointments Table */}
        <div className="bg-black/30 backdrop-blur-md border border-gray-700 rounded-xl overflow-hidden">
          <div className="p-6 border-b border-gray-700">
            <h2 className="text-2xl font-bold text-white">إدارة الحجوزات</h2>
          </div>

          {filteredAppointments.length === 0 ? (
            <div className="p-12 text-center">
              <CalendarIcon className="h-20 w-20 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400 text-lg">لا توجد حجوزات</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-700">
              {filteredAppointments.map((appointment, index) => (
                <motion.div
                  key={appointment.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="p-6 hover:bg-gray-800/50 transition-colors"
                >
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                    {/* Customer Info */}
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-3">
                        <div className="bg-blue-600/20 p-2 rounded-lg">
                          <UserIcon className="h-5 w-5 text-blue-400" />
                        </div>
                        <div>
                          <h3 className="text-white font-bold text-lg">{appointment.name}</h3>
                          <p className="text-gray-400 flex items-center">
                            <PhoneIcon className="h-4 w-4 ml-1" />
                            {appointment.phone}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center gap-6 text-sm">
                        <div className="flex items-center text-gray-300">
                          <CalendarIcon className="h-4 w-4 ml-2 text-purple-400" />
                          <span>{formatDate(appointment.date)}</span>
                        </div>
                        <div className="flex items-center text-gray-300">
                          <ClockIcon className="h-4 w-4 ml-2 text-orange-400" />
                          <span>{formatTime(appointment.time)}</span>
                        </div>
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>
                          {getStatusText(appointment.status)}
                        </span>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center gap-3">
                      {appointment.status === 'pending' ? (
                        <>
                          <button
                            onClick={() => handleStatusUpdate(
                              appointment.id,
                              'accepted',
                              appointment.date,
                              appointment.time,
                              appointment.customer_id,
                              appointment.name
                            )}
                            disabled={processingId === appointment.id}
                            className="flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:from-gray-600 disabled:to-gray-600 text-white rounded-lg transition-all font-medium shadow-lg hover:shadow-xl disabled:cursor-not-allowed"
                          >
                            <CheckCircleIcon className="h-5 w-5 ml-2" />
                            قبول الحجز
                          </button>
                          <button
                            onClick={() => handleStatusUpdate(
                              appointment.id,
                              'rejected',
                              appointment.date,
                              appointment.time,
                              appointment.customer_id,
                              appointment.name
                            )}
                            disabled={processingId === appointment.id}
                            className="flex items-center px-6 py-3 bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 disabled:from-gray-600 disabled:to-gray-600 text-white rounded-lg transition-all font-medium shadow-lg hover:shadow-xl disabled:cursor-not-allowed"
                          >
                            <XCircleIcon className="h-5 w-5 ml-2" />
                            رفض الحجز
                          </button>
                        </>
                      ) : (
                        <div className="text-right">
                          <p className="text-sm text-gray-400">تم المعالجة</p>
                          <p className="text-xs text-gray-500">
                            {new Date(appointment.created_at).toLocaleDateString('ar-EG')}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default AdminDashboard
