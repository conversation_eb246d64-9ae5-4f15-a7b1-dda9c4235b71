import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  ClockIcon, 
  UserIcon, 
  PhoneIcon,
  CalendarIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline'
import { appointmentService, notificationService } from '../lib/supabase'
import LoadingSpinner from './LoadingSpinner'

const AdminDashboard = ({ user, onLogout }) => {
  const [appointments, setAppointments] = useState([])
  const [loading, setLoading] = useState(true)
  const [processingId, setProcessingId] = useState(null)
  const [filter, setFilter] = useState('all') // all, pending, accepted, rejected

  useEffect(() => {
    loadAppointments()
  }, [])

  const loadAppointments = async () => {
    try {
      const data = await appointmentService.getAllAppointments()
      setAppointments(data)
    } catch (error) {
      console.error('Error loading appointments:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleStatusUpdate = async (appointmentId, newStatus, date, time, customerId, customerName) => {
    setProcessingId(appointmentId)

    try {
      await appointmentService.updateAppointmentStatus(appointmentId, newStatus)

      // Update slot availability based on status
      if (newStatus === 'accepted') {
        await appointmentService.markSlotAsBooked(date, time)
        // Send notification to customer
        await notificationService.createNotification(
          customerId,
          'تم قبول حجزك',
          `تم قبول حجزك ليوم ${date} في تمام الساعة ${time}. نتطلع لرؤيتك!`,
          'success'
        )
      } else if (newStatus === 'rejected') {
        await appointmentService.markSlotAsAvailable(date, time)
        // Send notification to customer
        await notificationService.createNotification(
          customerId,
          'تم رفض حجزك',
          `نعتذر، تم رفض حجزك ليوم ${date} في تمام الساعة ${time}. يرجى اختيار موعد آخر.`,
          'error'
        )
      }

      // Reload appointments
      await loadAppointments()
    } catch (error) {
      console.error('Error updating appointment:', error)
    } finally {
      setProcessingId(null)
    }
  }

  const handleLogout = () => {
    onLogout()
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-EG', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTime = (timeString) => {
    const [hours, minutes] = timeString.split(':')
    const hour = parseInt(hours)
    const ampm = hour >= 12 ? 'م' : 'ص'
    const displayHour = hour > 12 ? hour - 12 : hour === 0 ? 12 : hour
    return `${displayHour}:${minutes} ${ampm}`
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-50 text-yellow-700 border-yellow-500'
      case 'accepted':
        return 'bg-green-50 text-green-700 border-green-500'
      case 'rejected':
        return 'bg-red-50 text-red-700 border-red-500'
      default:
        return 'bg-gray-50 text-gray-700 border-gray-500'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'pending':
        return 'في الانتظار'
      case 'accepted':
        return 'مقبول'
      case 'rejected':
        return 'مرفوض'
      default:
        return status
    }
  }

  const filteredAppointments = appointments.filter(appointment => {
    if (filter === 'all') return true
    return appointment.status === filter
  })

  const stats = {
    total: appointments.length,
    pending: appointments.filter(a => a.status === 'pending').length,
    accepted: appointments.filter(a => a.status === 'accepted').length,
    rejected: appointments.filter(a => a.status === 'rejected').length
  }

  if (loading) {
    return <LoadingSpinner />
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Professional Header */}
      <header className="bg-black border-b-4 border-white shadow-xl">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 md:py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="bg-white p-2 sm:p-3 md:p-4 rounded-full ml-2 sm:ml-4 md:ml-6">
                <svg className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 text-black" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2L8 6h8l-4-4zm0 20l4-4H8l4 4zm-6-8l-4-4 4-4v8zm12 0v-8l4 4-4 4z"/>
                  <circle cx="12" cy="12" r="3" fill="black"/>
                </svg>
              </div>
              <div>
                <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-white">لوحة تحكم الإدارة</h1>
                <p className="text-gray-300 font-medium text-xs sm:text-sm md:text-base">إدارة الحجوزات والمواعيد</p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              className="flex items-center px-3 sm:px-4 md:px-6 py-2 sm:py-3 bg-white hover:bg-gray-200 text-black rounded-xl transition-colors font-bold text-xs sm:text-sm"
            >
              <ArrowRightOnRectangleIcon className="h-4 w-4 sm:h-5 sm:w-5 ml-1 sm:ml-2" />
              <span className="hidden sm:inline">تسجيل الخروج</span>
            </button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 md:py-8">
        {/* Professional Stats Dashboard */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6 lg:gap-8 mb-6 sm:mb-8 md:mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-white border-2 sm:border-4 border-black rounded-xl sm:rounded-2xl p-3 sm:p-4 md:p-6 lg:p-8 shadow-xl"
          >
            <div className="flex flex-col sm:flex-row items-center justify-between">
              <div className="text-center sm:text-left">
                <p className="text-gray-600 text-xs sm:text-sm font-bold">إجمالي الحجوزات</p>
                <p className="text-2xl sm:text-3xl md:text-4xl font-bold text-black">{stats.total}</p>
              </div>
              <CalendarIcon className="h-8 w-8 sm:h-12 sm:w-12 md:h-16 md:w-16 text-black mt-2 sm:mt-0" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-yellow-50 border-2 sm:border-4 border-yellow-500 rounded-xl sm:rounded-2xl p-3 sm:p-4 md:p-6 lg:p-8 shadow-xl"
          >
            <div className="flex flex-col sm:flex-row items-center justify-between">
              <div className="text-center sm:text-left">
                <p className="text-yellow-700 text-xs sm:text-sm font-bold">في الانتظار</p>
                <p className="text-2xl sm:text-3xl md:text-4xl font-bold text-yellow-600">{stats.pending}</p>
              </div>
              <ClockIcon className="h-8 w-8 sm:h-12 sm:w-12 md:h-16 md:w-16 text-yellow-600 mt-2 sm:mt-0" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-green-50 border-2 sm:border-4 border-green-500 rounded-xl sm:rounded-2xl p-3 sm:p-4 md:p-6 lg:p-8 shadow-xl"
          >
            <div className="flex flex-col sm:flex-row items-center justify-between">
              <div className="text-center sm:text-left">
                <p className="text-green-700 text-xs sm:text-sm font-bold">مقبولة</p>
                <p className="text-2xl sm:text-3xl md:text-4xl font-bold text-green-600">{stats.accepted}</p>
              </div>
              <CheckCircleIcon className="h-8 w-8 sm:h-12 sm:w-12 md:h-16 md:w-16 text-green-600 mt-2 sm:mt-0" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="bg-red-50 border-2 sm:border-4 border-red-500 rounded-xl sm:rounded-2xl p-3 sm:p-4 md:p-6 lg:p-8 shadow-xl"
          >
            <div className="flex flex-col sm:flex-row items-center justify-between">
              <div className="text-center sm:text-left">
                <p className="text-red-700 text-xs sm:text-sm font-bold">مرفوضة</p>
                <p className="text-2xl sm:text-3xl md:text-4xl font-bold text-red-600">{stats.rejected}</p>
              </div>
              <XCircleIcon className="h-8 w-8 sm:h-12 sm:w-12 md:h-16 md:w-16 text-red-600 mt-2 sm:mt-0" />
            </div>
          </motion.div>
        </div>

        {/* Professional Filter Buttons */}
        <div className="flex flex-wrap gap-2 sm:gap-3 md:gap-4 mb-6 sm:mb-8 bg-white rounded-xl sm:rounded-2xl p-2 sm:p-3 md:p-4 border-2 sm:border-4 border-black shadow-xl">
          {[
            { key: 'all', label: 'جميع الحجوزات', color: 'black' },
            { key: 'pending', label: 'في الانتظار', color: 'yellow' },
            { key: 'accepted', label: 'مقبولة', color: 'green' },
            { key: 'rejected', label: 'مرفوضة', color: 'red' }
          ].map((filterOption) => (
            <button
              key={filterOption.key}
              onClick={() => setFilter(filterOption.key)}
              className={`px-3 sm:px-4 md:px-6 lg:px-8 py-2 sm:py-3 md:py-4 rounded-lg sm:rounded-xl font-bold transition-all text-xs sm:text-sm md:text-base ${
                filter === filterOption.key
                  ? filterOption.color === 'black'
                    ? 'bg-black text-white shadow-lg scale-105'
                    : `bg-${filterOption.color}-500 text-white shadow-lg scale-105`
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200 hover:text-black'
              }`}
            >
              <span className="hidden sm:inline">{filterOption.label}</span>
              <span className="sm:hidden">
                {filterOption.key === 'all' ? 'الكل' :
                 filterOption.key === 'pending' ? 'انتظار' :
                 filterOption.key === 'accepted' ? 'مقبول' : 'مرفوض'}
              </span>
            </button>
          ))}
        </div>

        {/* Professional Appointments Table */}
        <div className="bg-white border-2 sm:border-4 border-black rounded-xl sm:rounded-2xl overflow-hidden shadow-2xl">
          <div className="p-4 sm:p-6 md:p-8 border-b-2 sm:border-b-4 border-black bg-black">
            <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-white">إدارة الحجوزات</h2>
          </div>

          {filteredAppointments.length === 0 ? (
            <div className="p-8 sm:p-12 md:p-16 text-center">
              <CalendarIcon className="h-16 w-16 sm:h-20 sm:w-20 md:h-24 md:w-24 text-gray-400 mx-auto mb-4 sm:mb-6" />
              <p className="text-gray-600 text-lg sm:text-xl font-medium">لا توجد حجوزات</p>
            </div>
          ) : (
            <div className="divide-y-2 sm:divide-y-4 divide-gray-200">
              {filteredAppointments.map((appointment, index) => (
                <motion.div
                  key={appointment.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="p-4 sm:p-6 md:p-8 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 sm:gap-6 md:gap-8">
                    {/* Customer Info */}
                    <div className="flex-1">
                      <div className="flex items-center gap-3 sm:gap-4 md:gap-6 mb-3 sm:mb-4">
                        <div className="bg-black p-2 sm:p-3 rounded-full">
                          <UserIcon className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-black font-bold text-lg sm:text-xl md:text-2xl">{appointment.name}</h3>
                          <p className="text-gray-600 flex items-center font-medium text-sm sm:text-base">
                            <PhoneIcon className="h-4 w-4 sm:h-5 sm:w-5 ml-1 sm:ml-2" />
                            {appointment.phone}
                          </p>
                        </div>
                      </div>

                      <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 md:gap-8 text-xs sm:text-sm">
                        <div className="flex items-center text-black">
                          <CalendarIcon className="h-4 w-4 sm:h-5 sm:w-5 ml-1 sm:ml-2 text-black" />
                          <span className="font-bold">{formatDate(appointment.date)}</span>
                        </div>
                        <div className="flex items-center text-black">
                          <ClockIcon className="h-4 w-4 sm:h-5 sm:w-5 ml-1 sm:ml-2 text-black" />
                          <span className="font-bold">{formatTime(appointment.time)}</span>
                        </div>
                        <span className={`px-2 sm:px-3 md:px-4 py-1 sm:py-2 rounded-lg sm:rounded-xl text-xs sm:text-sm font-bold border-2 ${getStatusColor(appointment.status)}`}>
                          {getStatusText(appointment.status)}
                        </span>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3 md:gap-4">
                      {appointment.status === 'pending' ? (
                        <>
                          <button
                            onClick={() => handleStatusUpdate(
                              appointment.id,
                              'accepted',
                              appointment.date,
                              appointment.time,
                              appointment.customer_id,
                              appointment.name
                            )}
                            disabled={processingId === appointment.id}
                            className="flex items-center justify-center px-3 sm:px-4 md:px-6 lg:px-8 py-2 sm:py-3 md:py-4 bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white rounded-lg sm:rounded-xl transition-all font-bold shadow-xl hover:shadow-2xl disabled:cursor-not-allowed border-2 border-green-600 text-xs sm:text-sm"
                          >
                            <CheckCircleIcon className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 ml-1 sm:ml-2" />
                            <span className="hidden sm:inline">قبول الحجز</span>
                            <span className="sm:hidden">قبول</span>
                          </button>
                          <button
                            onClick={() => handleStatusUpdate(
                              appointment.id,
                              'rejected',
                              appointment.date,
                              appointment.time,
                              appointment.customer_id,
                              appointment.name
                            )}
                            disabled={processingId === appointment.id}
                            className="flex items-center justify-center px-3 sm:px-4 md:px-6 lg:px-8 py-2 sm:py-3 md:py-4 bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-white rounded-lg sm:rounded-xl transition-all font-bold shadow-xl hover:shadow-2xl disabled:cursor-not-allowed border-2 border-red-600 text-xs sm:text-sm"
                          >
                            <XCircleIcon className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 ml-1 sm:ml-2" />
                            <span className="hidden sm:inline">رفض الحجز</span>
                            <span className="sm:hidden">رفض</span>
                          </button>
                        </>
                      ) : (
                        <div className="text-center sm:text-right bg-gray-100 p-2 sm:p-3 md:p-4 rounded-lg sm:rounded-xl border-2 border-gray-300">
                          <p className="text-xs sm:text-sm text-gray-600 font-bold">تم المعالجة</p>
                          <p className="text-xs text-gray-500 font-medium">
                            {new Date(appointment.created_at).toLocaleDateString('ar-EG')}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default AdminDashboard
