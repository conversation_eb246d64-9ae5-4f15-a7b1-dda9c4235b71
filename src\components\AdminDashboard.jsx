import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  ClockIcon, 
  UserIcon, 
  PhoneIcon,
  CalendarIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline'
import { appointmentService, notificationService } from '../lib/supabase'

const AdminDashboard = ({ user, onLogout }) => {
  const [appointments, setAppointments] = useState([])
  const [loading, setLoading] = useState(true)
  const [processingId, setProcessingId] = useState(null)
  const [filter, setFilter] = useState('all') // all, pending, accepted, rejected

  useEffect(() => {
    loadAppointments()
  }, [])

  const loadAppointments = async () => {
    try {
      const data = await appointmentService.getAllAppointments()
      setAppointments(data)
    } catch (error) {
      console.error('Error loading appointments:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleStatusUpdate = async (appointmentId, newStatus, date, time, customerId, customerName) => {
    setProcessingId(appointmentId)

    try {
      await appointmentService.updateAppointmentStatus(appointmentId, newStatus)

      // Update slot availability based on status
      if (newStatus === 'accepted') {
        await appointmentService.markSlotAsBooked(date, time)
        // Send notification to customer
        await notificationService.createNotification(
          customerId,
          'تم قبول حجزك',
          `تم قبول حجزك ليوم ${date} في تمام الساعة ${time}. نتطلع لرؤيتك!`,
          'success'
        )
      } else if (newStatus === 'rejected') {
        await appointmentService.markSlotAsAvailable(date, time)
        // Send notification to customer
        await notificationService.createNotification(
          customerId,
          'تم رفض حجزك',
          `نعتذر، تم رفض حجزك ليوم ${date} في تمام الساعة ${time}. يرجى اختيار موعد آخر.`,
          'error'
        )
      }

      // Reload appointments
      await loadAppointments()
    } catch (error) {
      console.error('Error updating appointment:', error)
    } finally {
      setProcessingId(null)
    }
  }

  const handleLogout = () => {
    onLogout()
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-EG', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTime = (timeString) => {
    const [hours, minutes] = timeString.split(':')
    const hour = parseInt(hours)
    const ampm = hour >= 12 ? 'م' : 'ص'
    const displayHour = hour > 12 ? hour - 12 : hour === 0 ? 12 : hour
    return `${displayHour}:${minutes} ${ampm}`
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-50 text-yellow-700 border-yellow-500'
      case 'accepted':
        return 'bg-green-50 text-green-700 border-green-500'
      case 'rejected':
        return 'bg-red-50 text-red-700 border-red-500'
      default:
        return 'bg-gray-50 text-gray-700 border-gray-500'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'pending':
        return 'في الانتظار'
      case 'accepted':
        return 'مقبول'
      case 'rejected':
        return 'مرفوض'
      default:
        return status
    }
  }

  const filteredAppointments = appointments.filter(appointment => {
    if (filter === 'all') return true
    return appointment.status === filter
  })

  const stats = {
    total: appointments.length,
    pending: appointments.filter(a => a.status === 'pending').length,
    accepted: appointments.filter(a => a.status === 'accepted').length,
    rejected: appointments.filter(a => a.status === 'rejected').length
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-white">جاري تحميل الحجوزات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Professional Header */}
      <header className="bg-black border-b-4 border-white shadow-xl">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="bg-white p-4 rounded-full ml-6">
                <svg className="w-10 h-10 text-black" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2L8 6h8l-4-4zm0 20l4-4H8l4 4zm-6-8l-4-4 4-4v8zm12 0v-8l4 4-4 4z"/>
                  <circle cx="12" cy="12" r="3" fill="black"/>
                </svg>
              </div>
              <div>
                <h1 className="text-4xl font-bold text-white">لوحة تحكم الإدارة</h1>
                <p className="text-gray-300 font-medium">إدارة الحجوزات والمواعيد</p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              className="flex items-center px-6 py-3 bg-white hover:bg-gray-200 text-black rounded-xl transition-colors font-bold"
            >
              <ArrowRightOnRectangleIcon className="h-5 w-5 ml-2" />
              تسجيل الخروج
            </button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Professional Stats Dashboard */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-white border-4 border-black rounded-2xl p-8 shadow-xl"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-600 text-sm font-bold">إجمالي الحجوزات</p>
                <p className="text-4xl font-bold text-black">{stats.total}</p>
              </div>
              <CalendarIcon className="h-16 w-16 text-black" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-yellow-50 border-4 border-yellow-500 rounded-2xl p-8 shadow-xl"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-yellow-700 text-sm font-bold">في الانتظار</p>
                <p className="text-4xl font-bold text-yellow-600">{stats.pending}</p>
              </div>
              <ClockIcon className="h-16 w-16 text-yellow-600" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-green-50 border-4 border-green-500 rounded-2xl p-8 shadow-xl"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-700 text-sm font-bold">مقبولة</p>
                <p className="text-4xl font-bold text-green-600">{stats.accepted}</p>
              </div>
              <CheckCircleIcon className="h-16 w-16 text-green-600" />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="bg-red-50 border-4 border-red-500 rounded-2xl p-8 shadow-xl"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-red-700 text-sm font-bold">مرفوضة</p>
                <p className="text-4xl font-bold text-red-600">{stats.rejected}</p>
              </div>
              <XCircleIcon className="h-16 w-16 text-red-600" />
            </div>
          </motion.div>
        </div>

        {/* Professional Filter Buttons */}
        <div className="flex flex-wrap gap-4 mb-8 bg-white rounded-2xl p-4 border-4 border-black shadow-xl">
          {[
            { key: 'all', label: 'جميع الحجوزات', color: 'black' },
            { key: 'pending', label: 'في الانتظار', color: 'yellow' },
            { key: 'accepted', label: 'مقبولة', color: 'green' },
            { key: 'rejected', label: 'مرفوضة', color: 'red' }
          ].map((filterOption) => (
            <button
              key={filterOption.key}
              onClick={() => setFilter(filterOption.key)}
              className={`px-8 py-4 rounded-xl font-bold transition-all ${
                filter === filterOption.key
                  ? filterOption.color === 'black'
                    ? 'bg-black text-white shadow-lg scale-105'
                    : `bg-${filterOption.color}-500 text-white shadow-lg scale-105`
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200 hover:text-black'
              }`}
            >
              {filterOption.label}
            </button>
          ))}
        </div>

        {/* Professional Appointments Table */}
        <div className="bg-white border-4 border-black rounded-2xl overflow-hidden shadow-2xl">
          <div className="p-8 border-b-4 border-black bg-black">
            <h2 className="text-3xl font-bold text-white">إدارة الحجوزات</h2>
          </div>

          {filteredAppointments.length === 0 ? (
            <div className="p-16 text-center">
              <CalendarIcon className="h-24 w-24 text-gray-400 mx-auto mb-6" />
              <p className="text-gray-600 text-xl font-medium">لا توجد حجوزات</p>
            </div>
          ) : (
            <div className="divide-y-4 divide-gray-200">
              {filteredAppointments.map((appointment, index) => (
                <motion.div
                  key={appointment.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="p-8 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-8">
                    {/* Customer Info */}
                    <div className="flex-1">
                      <div className="flex items-center gap-6 mb-4">
                        <div className="bg-black p-3 rounded-full">
                          <UserIcon className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-black font-bold text-2xl">{appointment.name}</h3>
                          <p className="text-gray-600 flex items-center font-medium">
                            <PhoneIcon className="h-5 w-5 ml-2" />
                            {appointment.phone}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center gap-8 text-sm">
                        <div className="flex items-center text-black">
                          <CalendarIcon className="h-5 w-5 ml-2 text-black" />
                          <span className="font-bold">{formatDate(appointment.date)}</span>
                        </div>
                        <div className="flex items-center text-black">
                          <ClockIcon className="h-5 w-5 ml-2 text-black" />
                          <span className="font-bold">{formatTime(appointment.time)}</span>
                        </div>
                        <span className={`px-4 py-2 rounded-xl text-sm font-bold border-2 ${getStatusColor(appointment.status)}`}>
                          {getStatusText(appointment.status)}
                        </span>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center gap-4">
                      {appointment.status === 'pending' ? (
                        <>
                          <button
                            onClick={() => handleStatusUpdate(
                              appointment.id,
                              'accepted',
                              appointment.date,
                              appointment.time,
                              appointment.customer_id,
                              appointment.name
                            )}
                            disabled={processingId === appointment.id}
                            className="flex items-center px-8 py-4 bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white rounded-xl transition-all font-bold shadow-xl hover:shadow-2xl disabled:cursor-not-allowed border-2 border-green-600"
                          >
                            <CheckCircleIcon className="h-6 w-6 ml-2" />
                            قبول الحجز
                          </button>
                          <button
                            onClick={() => handleStatusUpdate(
                              appointment.id,
                              'rejected',
                              appointment.date,
                              appointment.time,
                              appointment.customer_id,
                              appointment.name
                            )}
                            disabled={processingId === appointment.id}
                            className="flex items-center px-8 py-4 bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-white rounded-xl transition-all font-bold shadow-xl hover:shadow-2xl disabled:cursor-not-allowed border-2 border-red-600"
                          >
                            <XCircleIcon className="h-6 w-6 ml-2" />
                            رفض الحجز
                          </button>
                        </>
                      ) : (
                        <div className="text-right bg-gray-100 p-4 rounded-xl border-2 border-gray-300">
                          <p className="text-sm text-gray-600 font-bold">تم المعالجة</p>
                          <p className="text-xs text-gray-500 font-medium">
                            {new Date(appointment.created_at).toLocaleDateString('ar-EG')}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default AdminDashboard
