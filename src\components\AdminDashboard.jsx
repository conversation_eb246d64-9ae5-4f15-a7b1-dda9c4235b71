import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  ClockIcon, 
  UserIcon, 
  PhoneIcon,
  CalendarIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline'
import { appointmentService } from '../lib/supabase'

const AdminDashboard = ({ onLogout }) => {
  const [appointments, setAppointments] = useState([])
  const [loading, setLoading] = useState(true)
  const [processingId, setProcessingId] = useState(null)
  const [filter, setFilter] = useState('all') // all, pending, accepted, rejected

  useEffect(() => {
    loadAppointments()
  }, [])

  const loadAppointments = async () => {
    try {
      const data = await appointmentService.getAllAppointments()
      setAppointments(data)
    } catch (error) {
      console.error('Error loading appointments:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleStatusUpdate = async (appointmentId, newStatus, date, time) => {
    setProcessingId(appointmentId)
    
    try {
      await appointmentService.updateAppointmentStatus(appointmentId, newStatus)
      
      // Update slot availability based on status
      if (newStatus === 'accepted') {
        await appointmentService.markSlotAsBooked(date, time)
      } else if (newStatus === 'rejected') {
        await appointmentService.markSlotAsAvailable(date, time)
      }
      
      // Reload appointments
      await loadAppointments()
    } catch (error) {
      console.error('Error updating appointment:', error)
    } finally {
      setProcessingId(null)
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('adminLoggedIn')
    onLogout()
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-EG', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTime = (timeString) => {
    const [hours, minutes] = timeString.split(':')
    const hour = parseInt(hours)
    const ampm = hour >= 12 ? 'م' : 'ص'
    const displayHour = hour > 12 ? hour - 12 : hour === 0 ? 12 : hour
    return `${displayHour}:${minutes} ${ampm}`
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/50'
      case 'accepted':
        return 'bg-green-500/20 text-green-400 border-green-500/50'
      case 'rejected':
        return 'bg-red-500/20 text-red-400 border-red-500/50'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/50'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'pending':
        return 'في الانتظار'
      case 'accepted':
        return 'مقبول'
      case 'rejected':
        return 'مرفوض'
      default:
        return status
    }
  }

  const filteredAppointments = appointments.filter(appointment => {
    if (filter === 'all') return true
    return appointment.status === filter
  })

  const stats = {
    total: appointments.length,
    pending: appointments.filter(a => a.status === 'pending').length,
    accepted: appointments.filter(a => a.status === 'accepted').length,
    rejected: appointments.filter(a => a.status === 'rejected').length
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-white">جاري تحميل الحجوزات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="bg-slate-800/50 backdrop-blur-sm border-b border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-white">لوحة تحكم الإدارة</h1>
            <button
              onClick={handleLogout}
              className="flex items-center text-gray-300 hover:text-white transition-colors"
            >
              <ArrowRightOnRectangleIcon className="h-5 w-5 ml-2" />
              تسجيل الخروج
            </button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
          {[
            { label: 'إجمالي الحجوزات', value: stats.total, color: 'blue' },
            { label: 'في الانتظار', value: stats.pending, color: 'yellow' },
            { label: 'مقبولة', value: stats.accepted, color: 'green' },
            { label: 'مرفوضة', value: stats.rejected, color: 'red' }
          ].map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-xl p-6"
            >
              <p className="text-gray-400 text-sm">{stat.label}</p>
              <p className={`text-3xl font-bold text-${stat.color}-400`}>{stat.value}</p>
            </motion.div>
          ))}
        </div>

        {/* Filter Buttons */}
        <div className="flex flex-wrap gap-4 mb-8">
          {[
            { key: 'all', label: 'الكل' },
            { key: 'pending', label: 'في الانتظار' },
            { key: 'accepted', label: 'مقبولة' },
            { key: 'rejected', label: 'مرفوضة' }
          ].map((filterOption) => (
            <button
              key={filterOption.key}
              onClick={() => setFilter(filterOption.key)}
              className={`px-4 py-2 rounded-lg transition-colors ${
                filter === filterOption.key
                  ? 'bg-blue-600 text-white'
                  : 'bg-slate-700 text-gray-300 hover:bg-slate-600'
              }`}
            >
              {filterOption.label}
            </button>
          ))}
        </div>

        {/* Appointments List */}
        <div className="space-y-4">
          {filteredAppointments.length === 0 ? (
            <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-xl p-8 text-center">
              <p className="text-gray-400">لا توجد حجوزات</p>
            </div>
          ) : (
            filteredAppointments.map((appointment) => (
              <motion.div
                key={appointment.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-xl p-6"
              >
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                  {/* Appointment Info */}
                  <div className="flex-1 space-y-3">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center text-white">
                        <UserIcon className="h-5 w-5 ml-2 text-blue-400" />
                        <span className="font-semibold">{appointment.name}</span>
                      </div>
                      <div className="flex items-center text-gray-300">
                        <PhoneIcon className="h-5 w-5 ml-2 text-green-400" />
                        <span>{appointment.phone}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <div className="flex items-center text-gray-300">
                        <CalendarIcon className="h-5 w-5 ml-2 text-purple-400" />
                        <span>{formatDate(appointment.date)}</span>
                      </div>
                      <div className="flex items-center text-gray-300">
                        <ClockIcon className="h-5 w-5 ml-2 text-orange-400" />
                        <span>{formatTime(appointment.time)}</span>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <span className={`px-3 py-1 rounded-full text-sm border ${getStatusColor(appointment.status)}`}>
                        {getStatusText(appointment.status)}
                      </span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  {appointment.status === 'pending' && (
                    <div className="flex gap-3">
                      <button
                        onClick={() => handleStatusUpdate(appointment.id, 'accepted', appointment.date, appointment.time)}
                        disabled={processingId === appointment.id}
                        className="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg transition-colors"
                      >
                        <CheckCircleIcon className="h-5 w-5 ml-2" />
                        قبول
                      </button>
                      <button
                        onClick={() => handleStatusUpdate(appointment.id, 'rejected', appointment.date, appointment.time)}
                        disabled={processingId === appointment.id}
                        className="flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 text-white rounded-lg transition-colors"
                      >
                        <XCircleIcon className="h-5 w-5 ml-2" />
                        رفض
                      </button>
                    </div>
                  )}
                </div>
              </motion.div>
            ))
          )}
        </div>
      </div>
    </div>
  )
}

export default AdminDashboard
