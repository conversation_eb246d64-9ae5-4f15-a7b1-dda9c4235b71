import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  CalendarIcon,
  ClockIcon,
  BellIcon,
  UserIcon,
  ArrowRightOnRectangleIcon,
  PlusIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  TrashIcon
} from '@heroicons/react/24/outline'
import { appointmentService, notificationService } from '../lib/supabase'
import { formatDate, formatTime } from '../utils/helpers'
import useNotification from '../hooks/useNotification'
import Notification from './Notification'
import LoadingSpinner from './LoadingSpinner'

const CustomerDashboard = ({ user, onLogout }) => {
  const [appointments, setAppointments] = useState([])
  const [availableSlots, setAvailableSlots] = useState([])
  const [notifications, setNotifications] = useState([])
  const [loading, setLoading] = useState(true)
  const [showNotifications, setShowNotifications] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [activeTab, setActiveTab] = useState('appointments') // appointments, booking, profile

  const { notification, showSuccess, showError } = useNotification()

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      const [appointmentsData, slotsData, notificationsData] = await Promise.all([
        appointmentService.getCustomerAppointments(user.id),
        appointmentService.getAvailableSlots(),
        notificationService.getCustomerNotifications(user.id)
      ])

      setAppointments(appointmentsData)
      setAvailableSlots(slotsData)
      setNotifications(notificationsData)
    } catch (error) {
      console.error('Error loading data:', error)
      showError('حدث خطأ في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }



  const handleLogout = () => {
    localStorage.removeItem('user')
    localStorage.removeItem('userType')
    onLogout()
  }

  const markNotificationAsRead = async (notificationId) => {
    try {
      await notificationService.markAsRead(notificationId)
      setNotifications(prev =>
        prev.map(n => n.id === notificationId ? { ...n, is_read: true } : n)
      )
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  const deleteNotification = async (notificationId) => {
    try {
      // In a real app, you'd have a delete endpoint
      setNotifications(prev => prev.filter(n => n.id !== notificationId))
    } catch (error) {
      console.error('Error deleting notification:', error)
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'accepted':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      case 'rejected':
        return <XCircleIcon className="h-5 w-5 text-red-500" />
      default:
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
    }
  }

  const stats = {
    total: appointments.length,
    pending: appointments.filter(a => a.status === 'pending').length,
    accepted: appointments.filter(a => a.status === 'accepted').length,
    rejected: appointments.filter(a => a.status === 'rejected').length
  }

  if (loading) {
    return <LoadingSpinner />
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Notification
        type={notification.type}
        message={notification.message}
        isVisible={notification.isVisible}
      />

      {/* Elegant Header */}
      <header className="bg-white border-b-2 border-black shadow-lg">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="bg-black p-2 sm:p-3 md:p-4 rounded-full ml-2 sm:ml-4">
                <svg className="w-5 h-5 sm:w-6 sm:h-6 md:w-8 md:h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2L8 6h8l-4-4zm0 20l4-4H8l4 4zm-6-8l-4-4 4-4v8zm12 0v-8l4 4-4 4z"/>
                  <circle cx="12" cy="12" r="3" fill="white"/>
                </svg>
              </div>
              <div>
                <h1 className="text-lg sm:text-2xl md:text-3xl font-bold text-black">أهلاً {user.name}</h1>
                <p className="text-gray-600 text-xs sm:text-sm font-medium">حسابك الشخصي</p>
              </div>
            </div>

            <div className="flex items-center gap-2 sm:gap-3">
              {/* Notifications Button */}
              <button
                onClick={() => setShowNotifications(!showNotifications)}
                className="relative p-2 sm:p-3 bg-gray-100 hover:bg-gray-200 rounded-xl transition-colors border-2 border-gray-200"
              >
                <BellIcon className="h-5 w-5 sm:h-6 sm:w-6 text-black" />
                {notifications.filter(n => !n.is_read).length > 0 && (
                  <span className="absolute -top-1 -right-1 bg-black text-white text-xs rounded-full h-4 w-4 sm:h-5 sm:w-5 flex items-center justify-center animate-pulse font-bold">
                    {notifications.filter(n => !n.is_read).length}
                  </span>
                )}
              </button>

              <button
                onClick={handleLogout}
                className="flex items-center px-3 sm:px-4 md:px-6 py-2 sm:py-3 bg-black hover:bg-gray-800 text-white rounded-xl transition-colors font-bold text-xs sm:text-sm"
              >
                <ArrowRightOnRectangleIcon className="h-4 w-4 sm:h-5 sm:w-5 ml-1 sm:ml-2" />
                <span className="hidden sm:inline">خروج</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Notifications Dropdown */}
      <AnimatePresence>
        {showNotifications && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="absolute top-24 left-4 right-4 md:left-auto md:right-4 md:w-96 bg-white border-2 border-black rounded-xl p-6 z-50 shadow-2xl"
          >
            <h3 className="text-black font-bold mb-4 flex items-center text-lg">
              <BellIcon className="h-5 w-5 ml-2" />
              الإشعارات
            </h3>
            {notifications.length === 0 ? (
              <p className="text-gray-600 text-center py-4">لا توجد إشعارات</p>
            ) : (
              <div className="space-y-3 max-h-80 overflow-y-auto">
                {notifications.map((notif) => (
                  <div
                    key={notif.id}
                    className={`p-4 rounded-xl border-2 transition-all ${
                      notif.is_read
                        ? 'bg-gray-50 border-gray-200'
                        : 'bg-white border-black'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="text-black font-bold text-sm">{notif.title}</h4>
                        <p className="text-gray-600 text-xs mt-1">{notif.message}</p>
                        <p className="text-gray-500 text-xs mt-2">
                          {new Date(notif.created_at).toLocaleDateString('ar-EG')}
                        </p>
                      </div>
                      <div className="flex gap-1 mr-2">
                        {!notif.is_read && (
                          <button
                            onClick={() => markNotificationAsRead(notif.id)}
                            className="p-2 hover:bg-gray-100 rounded-lg"
                          >
                            <EyeIcon className="h-4 w-4 text-black" />
                          </button>
                        )}
                        <button
                          onClick={() => deleteNotification(notif.id)}
                          className="p-2 hover:bg-gray-100 rounded-lg"
                        >
                          <TrashIcon className="h-4 w-4 text-black" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 md:py-8">
        {/* Navigation Tabs */}
        <div className="flex gap-1 sm:gap-2 md:gap-3 mb-6 sm:mb-8 bg-white rounded-xl p-1 sm:p-2 border-2 border-black shadow-lg">
          {[
            { key: 'appointments', label: 'حجوزاتي', icon: CalendarIcon },
            { key: 'booking', label: 'حجز جديد', icon: PlusIcon },
            { key: 'profile', label: 'الملف الشخصي', icon: UserIcon }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              className={`flex-1 flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-2 py-2 sm:py-3 md:py-4 px-1 sm:px-2 md:px-4 rounded-lg transition-all font-bold text-xs sm:text-sm md:text-base ${
                activeTab === tab.key
                  ? 'bg-black text-white shadow-lg'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <tab.icon className="h-4 w-4 sm:h-5 sm:w-5" />
              <span className="hidden sm:inline">{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="bg-white border-2 border-black rounded-xl p-8 shadow-lg">
          {activeTab === 'appointments' && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-6"
            >
              <h2 className="text-3xl font-bold text-black mb-6">حجوزاتي</h2>

              {/* Quick Stats */}
              <div className="grid grid-cols-3 gap-2 sm:gap-4 md:gap-6 mb-6 sm:mb-8">
                <div className="bg-green-50 border-2 border-green-500 rounded-lg sm:rounded-xl p-3 sm:p-4 md:p-6 text-center">
                  <p className="text-green-600 text-xl sm:text-2xl md:text-3xl font-bold">{stats.accepted}</p>
                  <p className="text-green-700 text-xs sm:text-sm font-bold">مقبولة</p>
                </div>
                <div className="bg-yellow-50 border-2 border-yellow-500 rounded-lg sm:rounded-xl p-3 sm:p-4 md:p-6 text-center">
                  <p className="text-yellow-600 text-xl sm:text-2xl md:text-3xl font-bold">{stats.pending}</p>
                  <p className="text-yellow-700 text-xs sm:text-sm font-bold">في الانتظار</p>
                </div>
                <div className="bg-red-50 border-2 border-red-500 rounded-lg sm:rounded-xl p-3 sm:p-4 md:p-6 text-center">
                  <p className="text-red-600 text-xl sm:text-2xl md:text-3xl font-bold">{stats.rejected}</p>
                  <p className="text-red-700 text-xs sm:text-sm font-bold">مرفوضة</p>
                </div>
              </div>

              {appointments.length === 0 ? (
                <div className="text-center py-16">
                  <CalendarIcon className="h-24 w-24 text-gray-400 mx-auto mb-6" />
                  <p className="text-gray-600 text-xl mb-6 font-medium">لا توجد حجوزات حتى الآن</p>
                  <button
                    onClick={() => setActiveTab('booking')}
                    className="px-8 py-4 bg-black hover:bg-gray-800 text-white rounded-xl transition-all hover:scale-105 font-bold"
                  >
                    احجز موعدك الأول
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  {appointments.map((appointment) => (
                    <motion.div
                      key={appointment.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-gray-50 border-2 border-gray-200 rounded-xl p-6 hover:border-black transition-all"
                    >
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0">
                        <div className="space-y-2 sm:space-y-3">
                          <div className="flex items-center text-black">
                            <CalendarIcon className="h-5 w-5 sm:h-6 sm:w-6 ml-2 sm:ml-3 text-black" />
                            <span className="font-bold text-base sm:text-lg">{formatDate(appointment.date)}</span>
                          </div>
                          <div className="flex items-center text-gray-600">
                            <ClockIcon className="h-4 w-4 sm:h-5 sm:w-5 ml-2 text-gray-600" />
                            <span className="font-medium text-sm sm:text-base">{formatTime(appointment.time)}</span>
                          </div>
                        </div>
                        <div className="text-center sm:text-left">
                          {getStatusIcon(appointment.status)}
                          <p className={`text-xs sm:text-sm mt-1 sm:mt-2 font-bold ${
                            appointment.status === 'accepted' ? 'text-green-600' :
                            appointment.status === 'rejected' ? 'text-red-600' : 'text-yellow-600'
                          }`}>
                            {appointment.status === 'accepted' ? 'مقبول' :
                             appointment.status === 'rejected' ? 'مرفوض' : 'في الانتظار'}
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </motion.div>
          )}

          {activeTab === 'booking' && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-6"
            >
              <h2 className="text-3xl font-bold text-black mb-6">حجز موعد جديد</h2>

              <div className="bg-gray-100 border-2 border-gray-300 rounded-xl p-6 mb-8">
                <h3 className="text-black font-bold mb-3 text-lg">مواعيد العمل</h3>
                <p className="text-gray-700 text-sm font-medium">من 12:00 ظهراً إلى 12:00 منتصف الليل</p>
                <p className="text-gray-700 text-sm font-medium">جميع أيام الأسبوع</p>
              </div>

              {availableSlots.length === 0 ? (
                <div className="text-center py-16">
                  <ClockIcon className="h-24 w-24 text-gray-400 mx-auto mb-6" />
                  <p className="text-gray-600 text-xl font-medium">لا توجد مواعيد متاحة حالياً</p>
                </div>
              ) : (
                <div className="space-y-6">
                  <h3 className="text-black font-bold text-xl">اختر الموعد المناسب:</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 max-h-80 sm:max-h-96 overflow-y-auto">
                    {availableSlots.map((slot) => (
                      <motion.button
                        key={slot.id}
                        onClick={async () => {
                          setSubmitting(true)
                          try {
                            await appointmentService.createAppointment({
                              customer_id: user.id,
                              name: user.name,
                              phone: user.phone,
                              date: slot.date,
                              time: slot.time
                            })
                            showSuccess('تم إرسال طلب الحجز بنجاح!')
                            loadData()
                          } catch (error) {
                            console.error('Error booking appointment:', error)
                            showError('حدث خطأ في الحجز')
                          } finally {
                            setSubmitting(false)
                          }
                        }}
                        disabled={submitting}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="p-4 sm:p-5 md:p-6 bg-white border-2 border-gray-300 rounded-xl hover:border-black transition-all disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
                      >
                        <div className="text-black font-bold text-base sm:text-lg">{formatDate(slot.date)}</div>
                        <div className="text-gray-600 text-xs sm:text-sm mt-1 sm:mt-2 font-medium">{formatTime(slot.time)}</div>
                      </motion.button>
                    ))}
                  </div>
                </div>
              )}
            </motion.div>
          )}

          {activeTab === 'profile' && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-6"
            >
              <h2 className="text-3xl font-bold text-black mb-6">الملف الشخصي</h2>

              <div className="space-y-4 sm:space-y-6">
                <div className="bg-gray-50 border-2 border-gray-200 rounded-xl p-4 sm:p-6">
                  <label className="text-gray-600 text-xs sm:text-sm font-bold">الاسم</label>
                  <p className="text-black font-bold text-base sm:text-lg">{user.name}</p>
                </div>

                <div className="bg-gray-50 border-2 border-gray-200 rounded-xl p-4 sm:p-6">
                  <label className="text-gray-600 text-xs sm:text-sm font-bold">رقم الهاتف</label>
                  <p className="text-black font-bold text-base sm:text-lg">{user.phone}</p>
                </div>

                <div className="bg-gray-50 border-2 border-gray-200 rounded-xl p-4 sm:p-6">
                  <label className="text-gray-600 text-xs sm:text-sm font-bold">تاريخ التسجيل</label>
                  <p className="text-black font-bold text-base sm:text-lg">
                    {new Date(user.created_at).toLocaleDateString('ar-EG')}
                  </p>
                </div>

                <div className="bg-green-50 border-2 border-green-500 rounded-xl p-4 sm:p-6">
                  <h3 className="text-green-700 font-bold mb-3 sm:mb-4 text-base sm:text-lg">إحصائياتك</h3>
                  <div className="grid grid-cols-2 gap-4 sm:gap-6">
                    <div className="text-center">
                      <p className="text-green-600 text-xs sm:text-sm font-bold">إجمالي الحجوزات</p>
                      <p className="text-black text-2xl sm:text-3xl font-bold">{stats.total}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-green-600 text-xs sm:text-sm font-bold">الحجوزات المقبولة</p>
                      <p className="text-black text-2xl sm:text-3xl font-bold">{stats.accepted}</p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  )
}

export default CustomerDashboard
