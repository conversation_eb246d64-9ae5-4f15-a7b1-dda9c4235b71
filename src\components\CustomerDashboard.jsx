import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  CalendarIcon, 
  ClockIcon, 
  BellIcon, 
  UserIcon,
  ArrowRightOnRectangleIcon,
  PlusIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import { appointmentService, notificationService } from '../lib/supabase'
import { formatDate, formatTime, getStatusColor } from '../utils/helpers'
import { APPOINTMENT_STATUS_LABELS } from '../utils/constants'
import useNotification from '../hooks/useNotification'
import Notification from './Notification'

const CustomerDashboard = ({ user, onLogout }) => {
  const [appointments, setAppointments] = useState([])
  const [availableSlots, setAvailableSlots] = useState([])
  const [notifications, setNotifications] = useState([])
  const [loading, setLoading] = useState(true)
  const [showBookingModal, setShowBookingModal] = useState(false)
  const [selectedSlot, setSelectedSlot] = useState(null)
  const [submitting, setSubmitting] = useState(false)
  
  const { notification, showSuccess, showError } = useNotification()

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      const [appointmentsData, slotsData, notificationsData] = await Promise.all([
        appointmentService.getCustomerAppointments(user.id),
        appointmentService.getAvailableSlots(),
        notificationService.getCustomerNotifications(user.id)
      ])

      setAppointments(appointmentsData)
      setAvailableSlots(slotsData)
      setNotifications(notificationsData)
    } catch (error) {
      console.error('Error loading data:', error)
      showError('حدث خطأ في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  const handleBookAppointment = async () => {
    if (!selectedSlot) return

    setSubmitting(true)
    try {
      await appointmentService.createAppointment({
        customer_id: user.id,
        name: user.name,
        phone: user.phone,
        date: selectedSlot.date,
        time: selectedSlot.time
      })

      showSuccess('تم إرسال طلب الحجز بنجاح!')
      setShowBookingModal(false)
      setSelectedSlot(null)
      loadData()
    } catch (error) {
      console.error('Error booking appointment:', error)
      showError('حدث خطأ في الحجز')
    } finally {
      setSubmitting(false)
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('user')
    localStorage.removeItem('userType')
    onLogout()
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'accepted':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      case 'rejected':
        return <XCircleIcon className="h-5 w-5 text-red-500" />
      default:
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
    }
  }

  const stats = {
    total: appointments.length,
    pending: appointments.filter(a => a.status === 'pending').length,
    accepted: appointments.filter(a => a.status === 'accepted').length,
    rejected: appointments.filter(a => a.status === 'rejected').length
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-white">جاري تحميل البيانات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <Notification
        type={notification.type}
        message={notification.message}
        isVisible={notification.isVisible}
      />

      {/* Header */}
      <header className="bg-slate-800/50 backdrop-blur-sm border-b border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-2 rounded-lg ml-3">
                <UserIcon className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">مرحباً {user.name}</h1>
                <p className="text-gray-400 text-sm">لوحة تحكم العميل</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              {/* Notifications Badge */}
              <div className="relative">
                <BellIcon className="h-6 w-6 text-gray-400" />
                {notifications.filter(n => !n.is_read).length > 0 && (
                  <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {notifications.filter(n => !n.is_read).length}
                  </span>
                )}
              </div>

              <button
                onClick={() => setShowBookingModal(true)}
                className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                <PlusIcon className="h-5 w-5 ml-2" />
                حجز جديد
              </button>
              <button
                onClick={handleLogout}
                className="flex items-center text-gray-300 hover:text-white transition-colors"
              >
                <ArrowRightOnRectangleIcon className="h-5 w-5 ml-2" />
                تسجيل الخروج
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
          {[
            { label: 'إجمالي الحجوزات', value: stats.total, color: 'blue' },
            { label: 'في الانتظار', value: stats.pending, color: 'yellow' },
            { label: 'مقبولة', value: stats.accepted, color: 'green' },
            { label: 'مرفوضة', value: stats.rejected, color: 'red' }
          ].map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-xl p-6"
            >
              <p className="text-gray-400 text-sm">{stat.label}</p>
              <p className={`text-3xl font-bold text-${stat.color}-400`}>{stat.value}</p>
            </motion.div>
          ))}
        </div>

        {/* Notifications */}
        {notifications.length > 0 && (
          <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-xl p-6 mb-8">
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
              <BellIcon className="h-6 w-6 ml-3 text-yellow-500" />
              الإشعارات
            </h2>
            <div className="space-y-3">
              {notifications.slice(0, 3).map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 rounded-lg border ${
                    notification.type === 'success'
                      ? 'bg-green-500/20 border-green-500/50'
                      : notification.type === 'error'
                      ? 'bg-red-500/20 border-red-500/50'
                      : 'bg-blue-500/20 border-blue-500/50'
                  }`}
                >
                  <h3 className="text-white font-semibold mb-1">{notification.title}</h3>
                  <p className="text-gray-300 text-sm">{notification.message}</p>
                  <p className="text-gray-400 text-xs mt-2">
                    {new Date(notification.created_at).toLocaleDateString('ar-EG')}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Appointments List */}
        <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-xl p-6">
          <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
            <CalendarIcon className="h-6 w-6 ml-3 text-blue-500" />
            حجوزاتي
          </h2>

          {appointments.length === 0 ? (
            <div className="text-center py-12">
              <CalendarIcon className="h-16 w-16 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400 text-lg mb-4">لا توجد حجوزات حتى الآن</p>
              <button
                onClick={() => setShowBookingModal(true)}
                className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                احجز موعدك الأول
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {appointments.map((appointment) => (
                <motion.div
                  key={appointment.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-slate-700/50 border border-slate-600 rounded-lg p-4"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center text-white">
                        <CalendarIcon className="h-5 w-5 ml-2 text-purple-400" />
                        <span>{formatDate(appointment.date)}</span>
                      </div>
                      <div className="flex items-center text-gray-300">
                        <ClockIcon className="h-5 w-5 ml-2 text-orange-400" />
                        <span>{formatTime(appointment.time)}</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      {getStatusIcon(appointment.status)}
                      <span className={`px-3 py-1 rounded-full text-sm border ${getStatusColor(appointment.status)}`}>
                        {APPOINTMENT_STATUS_LABELS[appointment.status]}
                      </span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Booking Modal */}
      {showBookingModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-slate-800 border border-slate-700 rounded-xl p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto"
          >
            <h3 className="text-2xl font-bold text-white mb-6">حجز موعد جديد</h3>
            
            <div className="space-y-4 mb-6">
              <h4 className="text-lg font-semibold text-white">المواعيد المتاحة:</h4>
              {availableSlots.length === 0 ? (
                <p className="text-gray-400">لا توجد مواعيد متاحة حالياً</p>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-60 overflow-y-auto">
                  {availableSlots.map((slot) => (
                    <button
                      key={slot.id}
                      onClick={() => setSelectedSlot(slot)}
                      className={`p-3 rounded-lg border-2 transition-all text-right ${
                        selectedSlot?.id === slot.id
                          ? 'border-blue-500 bg-blue-500/20'
                          : 'border-slate-600 bg-slate-700/50 hover:border-slate-500'
                      }`}
                    >
                      <p className="text-white font-semibold">{formatDate(slot.date)}</p>
                      <p className="text-gray-300">{formatTime(slot.time)}</p>
                    </button>
                  ))}
                </div>
              )}
            </div>

            <div className="flex gap-4">
              <button
                onClick={handleBookAppointment}
                disabled={!selectedSlot || submitting}
                className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white py-3 rounded-lg transition-colors disabled:cursor-not-allowed"
              >
                {submitting ? 'جاري الحجز...' : 'تأكيد الحجز'}
              </button>
              <button
                onClick={() => {
                  setShowBookingModal(false)
                  setSelectedSlot(null)
                }}
                className="flex-1 bg-slate-600 hover:bg-slate-700 text-white py-3 rounded-lg transition-colors"
              >
                إلغاء
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  )
}

export default CustomerDashboard
