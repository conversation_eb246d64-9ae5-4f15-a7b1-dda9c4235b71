import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  CalendarIcon,
  ClockIcon,
  BellIcon,
  UserIcon,
  ArrowRightOnRectangleIcon,
  PlusIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  TrashIcon
} from '@heroicons/react/24/outline'
import { appointmentService, notificationService } from '../lib/supabase'
import { formatDate, formatTime } from '../utils/helpers'
import useNotification from '../hooks/useNotification'
import Notification from './Notification'

const CustomerDashboard = ({ user, onLogout }) => {
  const [appointments, setAppointments] = useState([])
  const [availableSlots, setAvailableSlots] = useState([])
  const [notifications, setNotifications] = useState([])
  const [loading, setLoading] = useState(true)
  const [showNotifications, setShowNotifications] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [activeTab, setActiveTab] = useState('appointments') // appointments, booking, profile

  const { notification, showSuccess, showError } = useNotification()

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      const [appointmentsData, slotsData, notificationsData] = await Promise.all([
        appointmentService.getCustomerAppointments(user.id),
        appointmentService.getAvailableSlots(),
        notificationService.getCustomerNotifications(user.id)
      ])

      setAppointments(appointmentsData)
      setAvailableSlots(slotsData)
      setNotifications(notificationsData)
    } catch (error) {
      console.error('Error loading data:', error)
      showError('حدث خطأ في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }



  const handleLogout = () => {
    localStorage.removeItem('user')
    localStorage.removeItem('userType')
    onLogout()
  }

  const markNotificationAsRead = async (notificationId) => {
    try {
      await notificationService.markAsRead(notificationId)
      setNotifications(prev =>
        prev.map(n => n.id === notificationId ? { ...n, is_read: true } : n)
      )
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  const deleteNotification = async (notificationId) => {
    try {
      // In a real app, you'd have a delete endpoint
      setNotifications(prev => prev.filter(n => n.id !== notificationId))
    } catch (error) {
      console.error('Error deleting notification:', error)
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'accepted':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      case 'rejected':
        return <XCircleIcon className="h-5 w-5 text-red-500" />
      default:
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
    }
  }

  const stats = {
    total: appointments.length,
    pending: appointments.filter(a => a.status === 'pending').length,
    accepted: appointments.filter(a => a.status === 'accepted').length,
    rejected: appointments.filter(a => a.status === 'rejected').length
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-white">جاري تحميل البيانات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
      <Notification
        type={notification.type}
        message={notification.message}
        isVisible={notification.isVisible}
      />

      {/* Modern Header */}
      <header className="bg-white/10 backdrop-blur-md border-b border-white/20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="bg-gradient-to-r from-pink-500 to-violet-500 p-3 rounded-full ml-4">
                <UserIcon className="h-7 w-7 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">أهلاً {user.name}</h1>
                <p className="text-purple-200 text-sm">حسابك الشخصي</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {/* Notifications Button */}
              <button
                onClick={() => setShowNotifications(!showNotifications)}
                className="relative p-2 bg-white/10 hover:bg-white/20 rounded-full transition-colors"
              >
                <BellIcon className="h-6 w-6 text-white" />
                {notifications.filter(n => !n.is_read).length > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse">
                    {notifications.filter(n => !n.is_read).length}
                  </span>
                )}
              </button>

              <button
                onClick={handleLogout}
                className="flex items-center px-4 py-2 bg-red-500/20 hover:bg-red-500/30 text-white rounded-lg transition-colors border border-red-500/30"
              >
                <ArrowRightOnRectangleIcon className="h-5 w-5 ml-2" />
                خروج
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Notifications Dropdown */}
      <AnimatePresence>
        {showNotifications && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="absolute top-20 left-4 right-4 md:left-auto md:right-4 md:w-96 bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-4 z-50"
          >
            <h3 className="text-white font-bold mb-4 flex items-center">
              <BellIcon className="h-5 w-5 ml-2" />
              الإشعارات
            </h3>
            {notifications.length === 0 ? (
              <p className="text-purple-200 text-center py-4">لا توجد إشعارات</p>
            ) : (
              <div className="space-y-3 max-h-80 overflow-y-auto">
                {notifications.map((notif) => (
                  <div
                    key={notif.id}
                    className={`p-3 rounded-lg border transition-all ${
                      notif.is_read
                        ? 'bg-white/5 border-white/10'
                        : 'bg-white/10 border-white/20'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="text-white font-semibold text-sm">{notif.title}</h4>
                        <p className="text-purple-200 text-xs mt-1">{notif.message}</p>
                        <p className="text-purple-300 text-xs mt-2">
                          {new Date(notif.created_at).toLocaleDateString('ar-EG')}
                        </p>
                      </div>
                      <div className="flex gap-1 mr-2">
                        {!notif.is_read && (
                          <button
                            onClick={() => markNotificationAsRead(notif.id)}
                            className="p-1 hover:bg-white/10 rounded"
                          >
                            <EyeIcon className="h-4 w-4 text-blue-400" />
                          </button>
                        )}
                        <button
                          onClick={() => deleteNotification(notif.id)}
                          className="p-1 hover:bg-white/10 rounded"
                        >
                          <TrashIcon className="h-4 w-4 text-red-400" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation Tabs */}
        <div className="flex gap-2 mb-8 bg-white/10 backdrop-blur-md rounded-xl p-2">
          {[
            { key: 'appointments', label: 'حجوزاتي', icon: CalendarIcon },
            { key: 'booking', label: 'حجز جديد', icon: PlusIcon },
            { key: 'profile', label: 'الملف الشخصي', icon: UserIcon }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-lg transition-all ${
                activeTab === tab.key
                  ? 'bg-gradient-to-r from-pink-500 to-violet-500 text-white shadow-lg'
                  : 'text-purple-200 hover:bg-white/10'
              }`}
            >
              <tab.icon className="h-5 w-5" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-6">
          {activeTab === 'appointments' && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-6"
            >
              <h2 className="text-2xl font-bold text-white mb-6">حجوزاتي</h2>

              {/* Quick Stats */}
              <div className="grid grid-cols-3 gap-4 mb-6">
                <div className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30 rounded-lg p-4 text-center">
                  <p className="text-green-400 text-2xl font-bold">{stats.accepted}</p>
                  <p className="text-green-300 text-sm">مقبولة</p>
                </div>
                <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-lg p-4 text-center">
                  <p className="text-yellow-400 text-2xl font-bold">{stats.pending}</p>
                  <p className="text-yellow-300 text-sm">في الانتظار</p>
                </div>
                <div className="bg-gradient-to-r from-red-500/20 to-pink-500/20 border border-red-500/30 rounded-lg p-4 text-center">
                  <p className="text-red-400 text-2xl font-bold">{stats.rejected}</p>
                  <p className="text-red-300 text-sm">مرفوضة</p>
                </div>
              </div>

              {appointments.length === 0 ? (
                <div className="text-center py-12">
                  <CalendarIcon className="h-20 w-20 text-purple-300 mx-auto mb-4" />
                  <p className="text-purple-200 text-lg mb-4">لا توجد حجوزات حتى الآن</p>
                  <button
                    onClick={() => setActiveTab('booking')}
                    className="px-6 py-3 bg-gradient-to-r from-pink-500 to-violet-500 text-white rounded-lg transition-all hover:scale-105"
                  >
                    احجز موعدك الأول
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  {appointments.map((appointment) => (
                    <motion.div
                      key={appointment.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-white/5 border border-white/10 rounded-lg p-4 hover:bg-white/10 transition-all"
                    >
                      <div className="flex items-center justify-between">
                        <div className="space-y-2">
                          <div className="flex items-center text-white">
                            <CalendarIcon className="h-5 w-5 ml-2 text-purple-400" />
                            <span className="font-semibold">{formatDate(appointment.date)}</span>
                          </div>
                          <div className="flex items-center text-purple-200">
                            <ClockIcon className="h-5 w-5 ml-2 text-pink-400" />
                            <span>{formatTime(appointment.time)}</span>
                          </div>
                        </div>
                        <div className="text-left">
                          {getStatusIcon(appointment.status)}
                          <p className={`text-sm mt-1 ${
                            appointment.status === 'accepted' ? 'text-green-400' :
                            appointment.status === 'rejected' ? 'text-red-400' : 'text-yellow-400'
                          }`}>
                            {appointment.status === 'accepted' ? 'مقبول' :
                             appointment.status === 'rejected' ? 'مرفوض' : 'في الانتظار'}
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </motion.div>
          )}

          {activeTab === 'booking' && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-6"
            >
              <h2 className="text-2xl font-bold text-white mb-6">حجز موعد جديد</h2>

              <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-lg p-4 mb-6">
                <h3 className="text-white font-semibold mb-2">مواعيد العمل</h3>
                <p className="text-purple-200 text-sm">من 12:00 ظهراً إلى 12:00 منتصف الليل</p>
                <p className="text-purple-200 text-sm">جميع أيام الأسبوع</p>
              </div>

              {availableSlots.length === 0 ? (
                <div className="text-center py-12">
                  <ClockIcon className="h-20 w-20 text-purple-300 mx-auto mb-4" />
                  <p className="text-purple-200 text-lg">لا توجد مواعيد متاحة حالياً</p>
                </div>
              ) : (
                <div className="space-y-4">
                  <h3 className="text-white font-semibold">اختر الموعد المناسب:</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                    {availableSlots.map((slot) => (
                      <motion.button
                        key={slot.id}
                        onClick={async () => {
                          setSubmitting(true)
                          try {
                            await appointmentService.createAppointment({
                              customer_id: user.id,
                              name: user.name,
                              phone: user.phone,
                              date: slot.date,
                              time: slot.time
                            })
                            showSuccess('تم إرسال طلب الحجز بنجاح!')
                            loadData()
                          } catch (error) {
                            console.error('Error booking appointment:', error)
                            showError('حدث خطأ في الحجز')
                          } finally {
                            setSubmitting(false)
                          }
                        }}
                        disabled={submitting}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="p-4 bg-gradient-to-r from-pink-500/20 to-violet-500/20 border border-pink-500/30 rounded-lg hover:from-pink-500/30 hover:to-violet-500/30 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <div className="text-white font-semibold">{formatDate(slot.date)}</div>
                        <div className="text-purple-200 text-sm mt-1">{formatTime(slot.time)}</div>
                      </motion.button>
                    ))}
                  </div>
                </div>
              )}
            </motion.div>
          )}

          {activeTab === 'profile' && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-6"
            >
              <h2 className="text-2xl font-bold text-white mb-6">الملف الشخصي</h2>

              <div className="space-y-4">
                <div className="bg-white/5 border border-white/10 rounded-lg p-4">
                  <label className="text-purple-200 text-sm">الاسم</label>
                  <p className="text-white font-semibold">{user.name}</p>
                </div>

                <div className="bg-white/5 border border-white/10 rounded-lg p-4">
                  <label className="text-purple-200 text-sm">رقم الهاتف</label>
                  <p className="text-white font-semibold">{user.phone}</p>
                </div>

                <div className="bg-white/5 border border-white/10 rounded-lg p-4">
                  <label className="text-purple-200 text-sm">تاريخ التسجيل</label>
                  <p className="text-white font-semibold">
                    {new Date(user.created_at).toLocaleDateString('ar-EG')}
                  </p>
                </div>

                <div className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-lg p-4">
                  <h3 className="text-green-400 font-semibold mb-2">إحصائياتك</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-green-300 text-sm">إجمالي الحجوزات</p>
                      <p className="text-white text-xl font-bold">{stats.total}</p>
                    </div>
                    <div>
                      <p className="text-green-300 text-sm">الحجوزات المقبولة</p>
                      <p className="text-white text-xl font-bold">{stats.accepted}</p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>


    </div>
  )
}

export default CustomerDashboard
