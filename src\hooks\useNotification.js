import { useState, useCallback } from 'react'

const useNotification = () => {
  const [notification, setNotification] = useState({
    isVisible: false,
    type: 'info',
    message: ''
  })

  const showNotification = useCallback((type, message, duration = 5000) => {
    setNotification({
      isVisible: true,
      type,
      message
    })

    // Auto hide after duration
    setTimeout(() => {
      setNotification(prev => ({ ...prev, isVisible: false }))
    }, duration)
  }, [])

  const hideNotification = useCallback(() => {
    setNotification(prev => ({ ...prev, isVisible: false }))
  }, [])

  const showSuccess = useCallback((message, duration) => {
    showNotification('success', message, duration)
  }, [showNotification])

  const showError = useCallback((message, duration) => {
    showNotification('error', message, duration)
  }, [showNotification])

  const showWarning = useCallback((message, duration) => {
    showNotification('warning', message, duration)
  }, [showNotification])

  const showInfo = useCallback((message, duration) => {
    showNotification('info', message, duration)
  }, [showNotification])

  return {
    notification,
    showNotification,
    hideNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo
  }
}

export default useNotification
