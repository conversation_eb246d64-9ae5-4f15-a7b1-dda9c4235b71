# موقع حلاقة عبدالله محمود 💈

منصة إلكترونية حديثة لحجز مواعيد الحلاقة مع لوحة تحكم للإدارة.

## 🎯 المميزات

### للعملاء:
- ✅ عرض المواعيد المتاحة
- ✅ حجز موعد بسهولة
- ✅ واجهة عربية أنيقة ومتجاوبة
- ✅ تأكيد الحجز فوري

### للحلاق (الإدارة):
- ✅ لوحة تحكم شاملة
- ✅ عرض جميع الحجوزات
- ✅ قبول أو رفض الحجوزات
- ✅ إحصائيات مفصلة
- ✅ إدارة المواعيد المتاحة

## 🛠️ التقنيات المستخدمة

- **React 19** - مكتبة واجهة المستخدم
- **Vite** - أداة البناء السريعة
- **Tailwind CSS** - إطار عمل التنسيق
- **Framer Motion** - مكتبة الحركة والتأثيرات
- **React Router** - التنقل بين الصفحات
- **Heroicons** - الأيقونات
- **Supabase** - قاعدة البيانات والخلفية
- **Cairo Font** - الخط العربي

## 🚀 التشغيل

### المتطلبات:
- Node.js 18+
- npm أو yarn

### خطوات التشغيل:

1. **تثبيت المكتبات:**
```bash
npm install
```

2. **تشغيل المشروع:**
```bash
npm run dev
```

3. **فتح المتصفح:**
```
http://localhost:5173
```

## 📱 الصفحات

### 1. الصفحة الرئيسية (`/`)
- عرض معلومات الحلاقة
- أزرار الانتقال للحجز
- معلومات التواصل

### 2. صفحة الحجز (`/appointments`)
- عرض المواعيد المتاحة
- نموذج الحجز
- تأكيد الطلب

### 3. تسجيل دخول الإدارة (`/admin`)
- تسجيل دخول آمن
- بيانات الدخول:
  - المستخدم: `admin`
  - كلمة المرور: `barber999`

### 4. لوحة تحكم الإدارة
- عرض جميع الحجوزات
- إحصائيات شاملة
- إدارة حالة الحجوزات

## 🗄️ قاعدة البيانات

### جدول `available_slots`:
- `id` - معرف فريد
- `date` - تاريخ الموعد
- `time` - وقت الموعد
- `is_booked` - حالة الحجز

### جدول `appointments`:
- `id` - معرف الحجز
- `name` - اسم العميل
- `phone` - رقم الهاتف
- `date` - التاريخ
- `time` - الوقت
- `status` - الحالة (pending/accepted/rejected)
- `created_at` - تاريخ الإنشاء

## 🎨 التصميم

- تصميم متجاوب (Responsive)
- ألوان عصرية (Dark Theme)
- خط Cairo العربي
- تأثيرات حركية ناعمة
- واجهة سهلة الاستخدام

## 📞 الدعم

للمساعدة أو الاستفسارات، يرجى التواصل مع المطور.

---

© 2025 حلاقة عبدالله محمود - جميع الحقوق محفوظة
