# موقع حلاقة عبدالله محمود 💈

منصة إلكترونية حديثة لحجز مواعيد الحلاقة مع لوحة تحكم للإدارة.

## 🌐 **رابط المشروع:**
[https://github.com/zeus-hunter/a-mahmoud.git](https://github.com/zeus-hunter/a-mahmoud.git)

## 🎯 المميزات

### للعملاء:
- ✅ عرض المواعيد المتاحة
- ✅ حجز موعد بسهولة
- ✅ واجهة عربية أنيقة ومتجاوبة
- ✅ تأكيد الحجز فوري

### للحلاق (الإدارة):
- ✅ لوحة تحكم شاملة
- ✅ عرض جميع الحجوزات
- ✅ قبول أو رفض الحجوزات
- ✅ إحصائيات مفصلة
- ✅ إدارة المواعيد المتاحة

## 🛠️ التقنيات المستخدمة

- **React 19** - مكتبة واجهة المستخدم
- **Vite** - أداة البناء السريعة
- **Tailwind CSS** - إطار عمل التنسيق
- **Framer Motion** - مكتبة الحركة والتأثيرات
- **React Router** - التنقل بين الصفحات
- **Heroicons** - الأيقونات
- **Supabase** - قاعدة البيانات والخلفية
- **Cairo Font** - الخط العربي

## 🚀 التشغيل

### المتطلبات:
- Node.js 18+
- npm أو yarn

### خطوات التشغيل:

1. **تثبيت المكتبات:**
```bash
npm install
```

2. **تشغيل المشروع:**
```bash
npm run dev
```

3. **فتح المتصفح:**
```
http://localhost:5173
```

## 📱 النظام الجديد

### 🔐 تسجيل الدخول الموحد
- صفحة واحدة لتسجيل دخول العملاء والإدارة
- إمكانية إنشاء حساب جديد للعملاء
- تبديل سهل بين وضع العميل والإدارة

### 👤 لوحة تحكم العميل
- عرض جميع الحجوزات الشخصية
- إحصائيات الحجوزات (معلقة، مقبولة، مرفوضة)
- حجز مواعيد جديدة من خلال نافذة منبثقة
- نظام إشعارات متقدم
- عداد الإشعارات غير المقروءة

### 🛠️ لوحة تحكم الإدارة
- عرض جميع حجوزات العملاء
- قبول أو رفض الحجوزات
- إرسال إشعارات تلقائية للعملاء
- إحصائيات شاملة
- إدارة المواعيد المتاحة

### 🔔 نظام الإشعارات
- إشعارات فورية عند قبول/رفض الحجز
- عرض الإشعارات في لوحة تحكم العميل
- عداد الإشعارات غير المقروءة
- أنواع مختلفة من الإشعارات (نجاح، خطأ، تحذير)

## 🗄️ قاعدة البيانات

### جدول `customers`:
- `id` - معرف العميل الفريد
- `name` - اسم العميل
- `phone` - رقم الهاتف (فريد)
- `password` - كلمة المرور
- `created_at` - تاريخ التسجيل

### جدول `available_slots`:
- `id` - معرف فريد
- `date` - تاريخ الموعد
- `time` - وقت الموعد
- `is_booked` - حالة الحجز

### جدول `appointments`:
- `id` - معرف الحجز
- `customer_id` - معرف العميل
- `name` - اسم العميل
- `phone` - رقم الهاتف
- `date` - التاريخ
- `time` - الوقت
- `status` - الحالة (pending/accepted/rejected)
- `created_at` - تاريخ الإنشاء

### جدول `notifications`:
- `id` - معرف الإشعار
- `customer_id` - معرف العميل
- `title` - عنوان الإشعار
- `message` - نص الإشعار
- `type` - نوع الإشعار (info/success/warning/error)
- `is_read` - حالة القراءة
- `created_at` - تاريخ الإنشاء

## 🎨 التصميم

- تصميم متجاوب (Responsive) لجميع الشاشات
- تصميم أبيض وأسود أنيق وشيك
- أيقونات حلاقة مخصصة (مقص وشفرة)
- خط Cairo العربي الجميل
- تأثيرات حركية ناعمة مع Framer Motion
- واجهة سهلة الاستخدام ومتطورة
- loader مقص يلف مع دوائر متحركة

## 🔐 تسجيل الدخول

### للإدارة:
- يمكن للمدير تسجيل الدخول باستخدام بيانات الاعتماد المحددة في قاعدة البيانات

### للعملاء:
- يمكن للعملاء إنشاء حسابات جديدة أو تسجيل الدخول بحساباتهم الموجودة

### إنشاء حساب جديد:
يمكن للعملاء إنشاء حسابات جديدة مباشرة من صفحة تسجيل الدخول.

## 🚀 الميزات الجديدة

- ✅ نظام مصادقة متكامل للعملاء والإدارة
- ✅ لوحات تحكم منفصلة ومخصصة
- ✅ نظام إشعارات فوري ومتقدم
- ✅ واجهة مستخدم محسنة بدون إيموجي
- ✅ تصميم متجاوب وعصري
- ✅ إدارة شاملة للحجوزات والمواعيد

## 📞 الدعم

للمساعدة أو الاستفسارات، يرجى التواصل مع المطور.

---

© 2025 حلاقة عبدالله محمود - جميع الحقوق محفوظة
