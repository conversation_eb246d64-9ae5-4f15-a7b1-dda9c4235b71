import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://byysowexyrszlqbowpnu.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ5eXNvd2V4eXJzemxxYm93cG51Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5Njg4ODIsImV4cCI6MjA2NTU0NDg4Mn0.uV6huLxiB8zaKpYzu9DQxL1C2Pr19T0qM0za-vTmsj0'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Helper functions for database operations
export const authService = {
  // Register new customer
  async registerCustomer(userData) {
    const { data, error } = await supabase
      .from('customers')
      .insert([{
        name: userData.name,
        phone: userData.phone,
        password: userData.password // In real app, hash this!
      }])
      .select()

    if (error) throw error
    return data[0]
  },

  // Login customer
  async loginCustomer(phone, password) {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('phone', phone)
      .eq('password', password) // In real app, compare hashed passwords!
      .single()

    if (error) throw error
    return data
  },

  // Login admin
  async loginAdmin(username, password) {
    // Simple admin check - in real app, use proper authentication
    if (username === 'admin' && password === 'barber999') {
      return { id: 'admin', username: 'admin', role: 'admin' }
    }
    throw new Error('Invalid credentials')
  }
}

export const appointmentService = {
  // Get all available slots that are not booked
  async getAvailableSlots() {
    const { data, error } = await supabase
      .from('available_slots')
      .select('*')
      .eq('is_booked', false)
      .order('date', { ascending: true })
      .order('time', { ascending: true })
    
    if (error) throw error
    return data
  },

  // Create a new appointment request
  async createAppointment(appointmentData) {
    const { data, error } = await supabase
      .from('appointments')
      .insert([appointmentData])
      .select()

    if (error) throw error
    return data[0]
  },

  // Get appointments for a specific customer
  async getCustomerAppointments(customerId) {
    const { data, error } = await supabase
      .from('appointments')
      .select('*')
      .eq('customer_id', customerId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Get all appointments (for admin)
  async getAllAppointments() {
    const { data, error } = await supabase
      .from('appointments')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  },

  // Update appointment status
  async updateAppointmentStatus(appointmentId, status) {
    const { data, error } = await supabase
      .from('appointments')
      .update({ status })
      .eq('id', appointmentId)
      .select()
    
    if (error) throw error
    return data[0]
  },

  // Mark slot as booked when appointment is accepted
  async markSlotAsBooked(date, time) {
    const { data, error } = await supabase
      .from('available_slots')
      .update({ is_booked: true })
      .eq('date', date)
      .eq('time', time)
      .select()
    
    if (error) throw error
    return data[0]
  },

  // Mark slot as available when appointment is rejected
  async markSlotAsAvailable(date, time) {
    const { data, error } = await supabase
      .from('available_slots')
      .update({ is_booked: false })
      .eq('date', date)
      .eq('time', time)
      .select()

    if (error) throw error
    return data[0]
  }
}

export const notificationService = {
  // Create notification for customer
  async createNotification(customerId, title, message, type = 'info') {
    const { data, error } = await supabase
      .from('notifications')
      .insert([{
        customer_id: customerId,
        title,
        message,
        type
      }])
      .select()

    if (error) throw error
    return data[0]
  },

  // Get notifications for customer
  async getCustomerNotifications(customerId) {
    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('customer_id', customerId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  },

  // Mark notification as read
  async markAsRead(notificationId) {
    const { data, error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', notificationId)
      .select()

    if (error) throw error
    return data[0]
  }
}
