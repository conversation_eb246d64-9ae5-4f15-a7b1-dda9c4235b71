import { useState, useEffect } from 'react'
import LoginPage from './components/HomePage' // Renamed HomePage to LoginPage
import CustomerDashboard from './components/CustomerDashboard'
import AdminDashboard from './components/AdminDashboard'

function App() {
  const [user, setUser] = useState(null)
  const [userType, setUserType] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Check if user is already logged in
    const savedUser = localStorage.getItem('user')
    const savedUserType = localStorage.getItem('userType')

    if (savedUser && savedUserType) {
      setUser(JSON.parse(savedUser))
      setUserType(savedUserType)
    }

    setLoading(false)
  }, [])

  const handleLogin = (userData, type) => {
    setUser(userData)
    setUserType(type)
  }

  const handleLogout = () => {
    setUser(null)
    setUserType(null)
    localStorage.removeItem('user')
    localStorage.removeItem('userType')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-white">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  // If not logged in, show login page
  if (!user || !userType) {
    return <LoginPage onLogin={handleLogin} />
  }

  // If logged in, show appropriate dashboard
  if (userType === 'admin') {
    return <AdminDashboard user={user} onLogout={handleLogout} />
  } else {
    return <CustomerDashboard user={user} onLogout={handleLogout} />
  }
}

export default App
