const LoadingSpinner = () => {
  return (
    <div className="min-h-screen bg-white flex items-center justify-center">
      <div className="text-center">
        <div className="relative w-24 h-24 mx-auto mb-6">
          {/* Outer Circle */}
          <div className="absolute inset-0 border-4 border-gray-200 rounded-full"></div>
          <div className="absolute inset-0 border-4 border-transparent border-t-black rounded-full animate-spin"></div>
          
          {/* Scissors Icon in Center */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="animate-pulse">
              <svg className="w-10 h-10 text-black" fill="currentColor" viewBox="0 0 24 24">
                <path d="M9.5 3L8 4.5L10.5 7L8 9.5L9.5 11L12 8.5L14.5 11L16 9.5L13.5 7L16 4.5L14.5 3L12 5.5L9.5 3Z"/>
                <circle cx="6" cy="6" r="2" fill="currentColor"/>
                <circle cx="6" cy="18" r="2" fill="currentColor"/>
                <path d="M20 4L8.5 15.5"/>
                <path d="M14.5 4L20 9.5"/>
              </svg>
            </div>
          </div>
          
          {/* Inner rotating dots */}
          <div className="absolute inset-2 border-2 border-transparent border-b-gray-400 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '2s' }}></div>
        </div>
        
        <div className="space-y-2">
          <p className="text-black text-xl font-bold">جاري التحميل...</p>
          <p className="text-gray-600 text-sm font-medium">يرجى الانتظار</p>
          
          {/* Loading dots animation */}
          <div className="flex justify-center space-x-1 mt-4">
            <div className="w-2 h-2 bg-black rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-black rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-black rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default LoadingSpinner
