import { useState } from 'react'
import { motion } from 'framer-motion'
import { UserIcon, LockClosedIcon, PhoneIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'
import { authService } from '../lib/supabase'
import useNotification from '../hooks/useNotification'
import Notification from './Notification'

const LoginPage = ({ onLogin }) => {
  const [isLogin, setIsLogin] = useState(true)
  const [userType, setUserType] = useState('customer') // customer or admin
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    password: '',
    confirmPassword: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)

  const { notification, showSuccess, showError } = useNotification()

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)

    try {
      if (userType === 'admin') {
        // Admin login
        const adminData = await authService.loginAdmin(formData.phone, formData.password)
        localStorage.setItem('user', JSON.stringify(adminData))
        localStorage.setItem('userType', 'admin')
        onLogin(adminData, 'admin')
        showSuccess('مرحباً بك في لوحة تحكم الإدارة')
      } else {
        // Customer login/register
        if (isLogin) {
          // Customer login
          const customerData = await authService.loginCustomer(formData.phone, formData.password)
          localStorage.setItem('user', JSON.stringify(customerData))
          localStorage.setItem('userType', 'customer')
          onLogin(customerData, 'customer')
          showSuccess(`مرحباً بك ${customerData.name}`)
        } else {
          // Customer register
          if (formData.password !== formData.confirmPassword) {
            showError('كلمات المرور غير متطابقة')
            return
          }

          const customerData = await authService.registerCustomer({
            name: formData.name,
            phone: formData.phone,
            password: formData.password
          })

          localStorage.setItem('user', JSON.stringify(customerData))
          localStorage.setItem('userType', 'customer')
          onLogin(customerData, 'customer')
          showSuccess(`مرحباً بك ${customerData.name}! تم إنشاء حسابك بنجاح`)
        }
      }
    } catch (error) {
      console.error('Auth error:', error)
      if (error.message.includes('duplicate')) {
        showError('رقم الهاتف مسجل مسبقاً')
      } else {
        showError('خطأ في البيانات المدخلة')
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
      <Notification
        type={notification.type}
        message={notification.message}
        isVisible={notification.isVisible}
      />

      <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10"></div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="relative max-w-md w-full mx-4"
      >
        <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-2xl p-8 shadow-2xl">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <UserIcon className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-white mb-2">حلاقة عبدالله محمود</h1>
            <p className="text-gray-400">
              {userType === 'admin' ? 'تسجيل دخول الإدارة' : (isLogin ? 'تسجيل الدخول' : 'إنشاء حساب جديد')}
            </p>
          </div>

          {/* User Type Toggle */}
          <div className="flex mb-6 bg-slate-700/50 rounded-lg p-1">
            <button
              type="button"
              onClick={() => setUserType('customer')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                userType === 'customer'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              عميل
            </button>
            <button
              type="button"
              onClick={() => setUserType('admin')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                userType === 'admin'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              إدارة
            </button>
          </div>

          {/* Login/Register Toggle for Customers */}
          {userType === 'customer' && (
            <div className="flex mb-6 bg-slate-700/50 rounded-lg p-1">
              <button
                type="button"
                onClick={() => setIsLogin(true)}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  isLogin
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-300 hover:text-white'
                }`}
              >
                تسجيل دخول
              </button>
              <button
                type="button"
                onClick={() => setIsLogin(false)}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                  !isLogin
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-300 hover:text-white'
                }`}
              >
                حساب جديد
              </button>
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Name field for customer registration */}
            {userType === 'customer' && !isLogin && (
              <div>
                <label className="block text-white font-semibold mb-2">
                  <UserIcon className="h-5 w-5 inline ml-2" />
                  الاسم الكامل
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors"
                  placeholder="أدخل اسمك الكامل"
                  required
                />
              </div>
            )}

            {/* Phone/Username field */}
            <div>
              <label className="block text-white font-semibold mb-2">
                {userType === 'admin' ? (
                  <>
                    <UserIcon className="h-5 w-5 inline ml-2" />
                    اسم المستخدم
                  </>
                ) : (
                  <>
                    <PhoneIcon className="h-5 w-5 inline ml-2" />
                    رقم الهاتف
                  </>
                )}
              </label>
              <input
                type={userType === 'admin' ? 'text' : 'tel'}
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors"
                placeholder={userType === 'admin' ? 'أدخل اسم المستخدم' : 'أدخل رقم هاتفك'}
                required
              />
            </div>

            {/* Password field */}
            <div>
              <label className="block text-white font-semibold mb-2">
                <LockClosedIcon className="h-5 w-5 inline ml-2" />
                كلمة المرور
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors pr-12"
                  placeholder="أدخل كلمة المرور"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
                >
                  {showPassword ? (
                    <EyeSlashIcon className="h-5 w-5" />
                  ) : (
                    <EyeIcon className="h-5 w-5" />
                  )}
                </button>
              </div>
            </div>

            {/* Confirm Password for customer registration */}
            {userType === 'customer' && !isLogin && (
              <div>
                <label className="block text-white font-semibold mb-2">
                  <LockClosedIcon className="h-5 w-5 inline ml-2" />
                  تأكيد كلمة المرور
                </label>
                <input
                  type="password"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors"
                  placeholder="أعد إدخال كلمة المرور"
                  required
                />
              </div>
            )}

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-600 text-white font-semibold py-4 rounded-lg transition-all duration-300 disabled:cursor-not-allowed transform hover:scale-105"
            >
              {loading ? 'جاري التحميل...' : (
                userType === 'admin' ? 'تسجيل الدخول' : (isLogin ? 'تسجيل الدخول' : 'إنشاء الحساب')
              )}
            </button>
          </form>

          {/* Demo Credentials for Admin */}
          {userType === 'admin' && (
            <div className="mt-6 p-4 bg-slate-700/30 rounded-lg border border-slate-600/50">
              <p className="text-gray-400 text-sm text-center mb-2">بيانات تجريبية:</p>
              <p className="text-gray-300 text-sm text-center">
                المستخدم: <span className="font-mono">admin</span>
              </p>
              <p className="text-gray-300 text-sm text-center">
                كلمة المرور: <span className="font-mono">barber999</span>
              </p>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  )
}

export default LoginPage
