import { useState } from 'react'
import { motion } from 'framer-motion'
import { UserIcon, LockClosedIcon, PhoneIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'
import { authService } from '../lib/supabase'
import useNotification from '../hooks/useNotification'
import Notification from './Notification'

const LoginPage = ({ onLogin }) => {
  const [isLogin, setIsLogin] = useState(true)
  const [userType, setUserType] = useState('customer') // customer or admin
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    password: '',
    confirmPassword: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)

  const { notification, showSuccess, showError } = useNotification()

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)

    try {
      if (userType === 'admin') {
        // Admin login
        const adminData = await authService.loginAdmin(formData.phone, formData.password)
        localStorage.setItem('user', JSON.stringify(adminData))
        localStorage.setItem('userType', 'admin')
        onLogin(adminData, 'admin')
        showSuccess('مرحباً بك في لوحة تحكم الإدارة')
      } else {
        // Customer login/register
        if (isLogin) {
          // Customer login
          const customerData = await authService.loginCustomer(formData.phone, formData.password)
          localStorage.setItem('user', JSON.stringify(customerData))
          localStorage.setItem('userType', 'customer')
          onLogin(customerData, 'customer')
          showSuccess(`مرحباً بك ${customerData.name}`)
        } else {
          // Customer register
          if (formData.password !== formData.confirmPassword) {
            showError('كلمات المرور غير متطابقة')
            return
          }

          const customerData = await authService.registerCustomer({
            name: formData.name,
            phone: formData.phone,
            password: formData.password
          })

          localStorage.setItem('user', JSON.stringify(customerData))
          localStorage.setItem('userType', 'customer')
          onLogin(customerData, 'customer')
          showSuccess(`مرحباً بك ${customerData.name}! تم إنشاء حسابك بنجاح`)
        }
      }
    } catch (error) {
      console.error('Auth error:', error)
      if (error.message.includes('duplicate')) {
        showError('رقم الهاتف مسجل مسبقاً')
      } else {
        showError('خطأ في البيانات المدخلة')
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-white flex items-center justify-center relative overflow-hidden">
      <Notification
        type={notification.type}
        message={notification.message}
        isVisible={notification.isVisible}
      />

      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-white">
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 w-32 h-32 border-2 border-black rounded-full"></div>
          <div className="absolute top-32 right-20 w-24 h-24 border border-black rounded-full"></div>
          <div className="absolute bottom-20 left-32 w-40 h-40 border border-black rounded-full"></div>
          <div className="absolute bottom-32 right-10 w-28 h-28 border-2 border-black rounded-full"></div>
        </div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="relative max-w-md w-full mx-4 z-10"
      >
        <div className="bg-white border-2 border-black rounded-3xl p-4 sm:p-6 md:p-8 shadow-2xl">
          {/* Header */}
          <div className="text-center mb-6 sm:mb-8">
            {/* Barber Icon */}
            <div className="relative mx-auto mb-4 sm:mb-6 w-16 h-16 sm:w-20 sm:h-20">
              <div className="absolute inset-0 bg-black rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 sm:w-10 sm:h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2L8 6h8l-4-4zm0 20l4-4H8l4 4zm-6-8l-4-4 4-4v8zm12 0v-8l4 4-4 4z"/>
                  <circle cx="12" cy="12" r="3" fill="white"/>
                </svg>
              </div>
              <div className="absolute -top-1 -right-1 w-5 h-5 sm:w-6 sm:h-6 bg-black rounded-full flex items-center justify-center">
                <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9.5 3L8 4.5L10.5 7L8 9.5L9.5 11L12 8.5L14.5 11L16 9.5L13.5 7L16 4.5L14.5 3L12 5.5L9.5 3Z"/>
                </svg>
              </div>
            </div>

            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-black mb-2 tracking-tight">حلاقة عبدالله محمود</h1>
            <div className="w-12 sm:w-16 h-1 bg-black mx-auto mb-3 sm:mb-4"></div>
            <p className="text-gray-600 font-medium text-sm sm:text-base">
              {userType === 'admin' ? 'تسجيل دخول الإدارة' : (isLogin ? 'تسجيل الدخول' : 'إنشاء حساب جديد')}
            </p>
          </div>

          {/* User Type Toggle */}
          <div className="flex gap-2 sm:gap-3 mb-4 sm:mb-6 bg-gray-100 rounded-xl p-1 border border-gray-200">
            <button
              type="button"
              onClick={() => setUserType('customer')}
              className={`flex-1 py-2 sm:py-3 px-2 sm:px-4 rounded-lg text-xs sm:text-sm font-bold transition-all ${
                userType === 'customer'
                  ? 'bg-black text-white shadow-lg'
                  : 'text-gray-600 hover:text-black'
              }`}
            >
              عميل
            </button>
            <button
              type="button"
              onClick={() => setUserType('admin')}
              className={`flex-1 py-2 sm:py-3 px-2 sm:px-4 rounded-lg text-xs sm:text-sm font-bold transition-all ${
                userType === 'admin'
                  ? 'bg-black text-white shadow-lg'
                  : 'text-gray-600 hover:text-black'
              }`}
            >
              إدارة
            </button>
          </div>

          {/* Login/Register Toggle for Customers */}
          {userType === 'customer' && (
            <div className="flex gap-2 sm:gap-3 mb-4 sm:mb-6 bg-gray-100 rounded-xl p-1 border border-gray-200">
              <button
                type="button"
                onClick={() => setIsLogin(true)}
                className={`flex-1 py-2 sm:py-3 px-2 sm:px-4 rounded-lg text-xs sm:text-sm font-bold transition-all ${
                  isLogin
                    ? 'bg-black text-white shadow-lg'
                    : 'text-gray-600 hover:text-black'
                }`}
              >
                تسجيل دخول
              </button>
              <button
                type="button"
                onClick={() => setIsLogin(false)}
                className={`flex-1 py-2 sm:py-3 px-2 sm:px-4 rounded-lg text-xs sm:text-sm font-bold transition-all ${
                  !isLogin
                    ? 'bg-black text-white shadow-lg'
                    : 'text-gray-600 hover:text-black'
                }`}
              >
                حساب جديد
              </button>
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
            {/* Name field for customer registration */}
            {userType === 'customer' && !isLogin && (
              <div>
                <label className="block text-black font-bold mb-2 sm:mb-3 text-sm sm:text-base">
                  <UserIcon className="h-4 w-4 sm:h-5 sm:w-5 inline ml-2" />
                  الاسم الكامل
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-3 sm:px-4 py-3 sm:py-4 bg-gray-50 border-2 border-gray-200 rounded-xl text-black placeholder-gray-500 focus:border-black focus:outline-none transition-all font-medium text-sm sm:text-base"
                  placeholder="أدخل اسمك الكامل"
                  required
                />
              </div>
            )}

            {/* Phone/Username field */}
            <div>
              <label className="block text-black font-bold mb-2 sm:mb-3 text-sm sm:text-base">
                {userType === 'admin' ? (
                  <>
                    <UserIcon className="h-4 w-4 sm:h-5 sm:w-5 inline ml-2" />
                    اسم المستخدم
                  </>
                ) : (
                  <>
                    <PhoneIcon className="h-4 w-4 sm:h-5 sm:w-5 inline ml-2" />
                    رقم الهاتف
                  </>
                )}
              </label>
              <input
                type={userType === 'admin' ? 'text' : 'tel'}
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className="w-full px-3 sm:px-4 py-3 sm:py-4 bg-gray-50 border-2 border-gray-200 rounded-xl text-black placeholder-gray-500 focus:border-black focus:outline-none transition-all font-medium text-sm sm:text-base"
                placeholder={userType === 'admin' ? 'أدخل اسم المستخدم' : 'أدخل رقم هاتفك'}
                required
              />
            </div>

            {/* Password field */}
            <div>
              <label className="block text-black font-bold mb-2 sm:mb-3 text-sm sm:text-base">
                <LockClosedIcon className="h-4 w-4 sm:h-5 sm:w-5 inline ml-2" />
                كلمة المرور
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="w-full px-3 sm:px-4 py-3 sm:py-4 bg-gray-50 border-2 border-gray-200 rounded-xl text-black placeholder-gray-500 focus:border-black focus:outline-none transition-all font-medium pr-10 sm:pr-12 text-sm sm:text-base"
                  placeholder="أدخل كلمة المرور"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-2 sm:left-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-black transition-colors"
                >
                  {showPassword ? (
                    <EyeSlashIcon className="h-4 w-4 sm:h-5 sm:w-5" />
                  ) : (
                    <EyeIcon className="h-4 w-4 sm:h-5 sm:w-5" />
                  )}
                </button>
              </div>
            </div>

            {/* Confirm Password for customer registration */}
            {userType === 'customer' && !isLogin && (
              <div>
                <label className="block text-black font-bold mb-2 sm:mb-3 text-sm sm:text-base">
                  <LockClosedIcon className="h-4 w-4 sm:h-5 sm:w-5 inline ml-2" />
                  تأكيد كلمة المرور
                </label>
                <input
                  type="password"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className="w-full px-3 sm:px-4 py-3 sm:py-4 bg-gray-50 border-2 border-gray-200 rounded-xl text-black placeholder-gray-500 focus:border-black focus:outline-none transition-all font-medium text-sm sm:text-base"
                  placeholder="أعد إدخال كلمة المرور"
                  required
                />
              </div>
            )}

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-black hover:bg-gray-800 disabled:bg-gray-400 text-white font-bold py-3 sm:py-4 rounded-xl transition-all duration-300 disabled:cursor-not-allowed transform hover:scale-105 shadow-lg text-sm sm:text-base"
            >
              {loading ? 'جاري التحميل...' : (
                userType === 'admin' ? 'تسجيل الدخول' : (isLogin ? 'تسجيل الدخول' : 'إنشاء الحساب')
              )}
            </button>
          </form>

          {/* Demo Credentials for Admin */}
          {userType === 'admin' && (
            <div className="mt-4 sm:mt-6 p-3 sm:p-4 bg-gray-100 rounded-xl border-2 border-gray-200">
              <p className="text-gray-600 text-xs sm:text-sm text-center mb-2 font-medium">بيانات تجريبية:</p>
              <p className="text-black text-xs sm:text-sm text-center font-bold">
                المستخدم: <span className="font-mono bg-black text-white px-1.5 sm:px-2 py-0.5 sm:py-1 rounded text-xs sm:text-sm">admin</span>
              </p>
              <p className="text-black text-xs sm:text-sm text-center font-bold mt-1">
                كلمة المرور: <span className="font-mono bg-black text-white px-1.5 sm:px-2 py-0.5 sm:py-1 rounded text-xs sm:text-sm">barber999</span>
              </p>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  )
}

export default LoginPage
